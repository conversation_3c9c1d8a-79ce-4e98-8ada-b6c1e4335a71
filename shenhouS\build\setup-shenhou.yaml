---
- name: Setup shenhouS
  hosts: all
  become: true
  vars:
    go_program_path: /usr/local/bin/shenhouS
    service_name: shenhouS
    service_unit_file: /etc/systemd/system/shenhouS.service
  
  tasks:
    - name: Stop service if running
      systemd:
        name: "{{ service_name }}"
        state: stopped
      ignore_errors: yes

    # 复制二进制文件
    - name: Upload Go program binary
      copy:
        src: /data/go/shenhouS/shenhouS
        dest: "{{ go_program_path }}"
        mode: '0755'
        force: yes  # 强制复制，确保每次都覆盖

    - name: Check if systemd service file exists
      stat:
        path: "{{ service_unit_file }}"
      register: service_unit_file_stat

    # 创建systemctl启动文件
    - name: Ensure systemd service file exists
      copy:
        dest: "{{ service_unit_file }}"
        content: |
          [Unit]
          Description=shenhouS
          After=network.target

          [Service]
          ExecStart={{ go_program_path }}
          Restart=always
          User=nobody
          Group=nogroup

          [Install]
          WantedBy=multi-user.target
      when: not service_unit_file_stat.stat.exists

    - name: Reload systemd daemon
      systemd:
        daemon_reload: yes
      when: service_unit_file_stat is changed

    - name: Start service
      systemd:
        name: "{{ service_name }}"
        enabled: yes
        state: started