package grab

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitzhang87/shenhouS/internal/dao"
	"github.com/limitzhang87/shenhouS/internal/utils"
	"math/rand"
	"net/http"
	"time"
)

type GetListFunc func(info *CurlInfo) (string, http.Header, error)

// GetList 获取订单列列表
func GetList(sid string, info *CurlInfo) (string, http.Header, error) {
	var urlStr string
	var reqHeader map[string]string
	body := info.Body
	body["sid"] = sid
	//body["pageSessionId"] = utils.GetGuid()
	var reqBody string

	i := rand.Intn(4)
	i = 0
	switch i {
	case 0, 1:
		// APP v1版本
		urlStr = "https://taxiapi.hellobike.com/api"
		action := "hitch.driver.journeyList"
		reqHeader, reqBody = GetAppSendHeaderAndBody(GetAppParam{
			Action:    action,
			Url:       urlStr,
			Sid:       sid,
			Params:    body,
			SecretKey: info.SecretKey,
		})

	//case 1:
	//	// APP v2版本
	//	urlStr = "https://taxiapi.hellobike.com/api"
	//	reqHeader = GetHeaderAppV2(sid)
	//	temp, _ := json.Marshal(body)
	//	reqBodyStr, _ := utils.AesEncrypt(temp)
	//	reqBody = []byte(reqBodyStr)
	case 2, 3:
		// 小程序 v1版本
		urlStr = "https://taxiapi.hellobike.com/api?hitch.driver.journeyList"
		reqHeader = GetHeaderWx(sid)
		reqBodyByte, _ := json.Marshal(body)
		reqBody = string(reqBodyByte)
		//case 3:
		//	// 小程序 v2版本
		//	urlStr = "https://taxiapi.hellobike.com/api?hitch.driver.journeyListV2"
		//	reqHeader = GetHeaderWx(sid)
		//	reqBody, _ = json.Marshal(body)
	}

	rspBody, header, err := info.Client.Post(urlStr, []byte(reqBody), reqHeader)
	if i == 0 || i == 1 {
		rspBody = DecryptBody(rspBody, info.SecretKey)
	}
	return rspBody, header, err
}

// CurlSubmit 发起抢单
func CurlSubmit(req *dao.SubmitReq, info *CurlInfo) (string, http.Header, error) {
	var bodyStr string
	var rspHeader http.Header
	var err error
	if req.ApiType == ApiTypeApp {
		beforeSubmit(req, info)
		bodyStr, rspHeader, err = curlSubmitApp(req, info)
	} else {
		bodyStr, rspHeader, err = curlSubmitWx(req, info)
	}
	fmt.Println("抢单结果App", bodyStr)
	return bodyStr, rspHeader, err
}

func curlSubmitTest() (string, http.Header, error) {
	time.Sleep(2 * time.Second)
	data := ""
	randInt := rand.Intn(10)
	switch randInt {
	case 0:
		data = `{"code":0,"msg":"ok","data":{"driverJourneyGuid":"JP2024080840237200001747499079","orderId":"JP2024080840237200001747499079","userGuid":"d1ac4f69b9ea421eaeb81abba54a9fe7","userNewId":1747499079,"sourceType":1,"orderStatus":20,"planStartTime":"1723168500000","coordinateSystem":"GCJ-02","publishTime":1723050125135,"seatCount":4,"systemCode":"64","clientVersion":"6.69.0","paxJourneyGuidArray":["JP2024080730763800001221256598"],"currentPaxJourneyGuidArray":["JP2024080730763800001221256598"],"totalDistance":0,"poolFlowPage":false}}`
	case 1:
		data = `{"code":865,"msg":"接单确认中，约31秒后出结果，请耐心等待"}`
	default:
		data = `{"code":880,"msg":"加入失败，你当前已有订单在PK中，请稍后查看PK结果"}`
	}
	data = `{"code":0,"msg":"ok","data":{"joinPkSuccess":true}}`
	dataByte, _ := utils.AesEncrypt([]byte(data))
	return dataByte, nil, nil
}

// phoneCheck 抢单前号码检查
func phoneCheck(req *dao.SubmitReq, info *CurlInfo) {
	urlStr := "https://taxiapi.hellobike.com/api"
	action := "hitch.usercenter.phone.check"
	params := map[string]interface{}{
		"action":      action,
		"clientId":    "",
		"systemCode":  "62",
		"ticket":      req.Ticket,
		"token":       req.Token,
		"version":     AppVersion,
		"adCode":      req.StartAddr.AdCode,
		"cityCode":    req.StartAddr.CityCode,
		"lat":         req.StartAddr.Lat,
		"lon":         req.StartAddr.Lon,
		"mobilePhone": req.Phone,
		"sid":         req.Sid,
	}

	secretKey := req.SecretKey

	header, data := GetAppSendHeaderAndBody(GetAppParam{
		Action:    action,
		Url:       urlStr,
		Sid:       req.Sid,
		Params:    params,
		SecretKey: secretKey,
	})
	rspBody, _, err := info.Client.Post(urlStr, []byte(data), header)
	if err != nil {
		fmt.Println(err)
		return
	}
	rspBody = DecryptBody(rspBody, secretKey)
	fmt.Println(rspBody)
}

// prePeer 抢单前准备
func prePeer(req *dao.SubmitReq, info *CurlInfo) {
	urlStr := "https://taxiapi.hellobike.com/api"
	action := "hitch.driver.prePeer"
	params := map[string]interface{}{
		"action":        action,
		"clientId":      "",
		"systemCode":    "62",
		"ticket":        req.Ticket,
		"token":         req.Token,
		"version":       AppVersion,
		"adCode":        req.StartAddr.AdCode,
		"cityCode":      req.StartAddr.CityCode,
		"lat":           req.StartAddr.Lat,
		"lon":           req.StartAddr.Lon,
		"driverOrderNo": "",
		"paxOrderNo":    req.Id,
		"sid":           req.Sid,
	}
	secretKey := req.SecretKey
	header, data := GetAppSendHeaderAndBody(GetAppParam{
		Action:    action,
		Url:       urlStr,
		Sid:       req.Sid,
		Params:    params,
		SecretKey: secretKey,
	})
	rspBody, _, err := info.Client.Post(urlStr, []byte(data), header)
	if err != nil {
		fmt.Println(err)
		return
	}
	rspBody = DecryptBody(rspBody, secretKey)
	fmt.Println(rspBody)
}

func warningBeforeAcceptOrder(req *dao.SubmitReq, info *CurlInfo) {
	urlStr := "https://taxiapi.hellobike.com/api"
	action := "hitch.driver.warningBeforeAcceptOrder"
	startLat := utils.ToFloat64(req.StartAddr.Lat)
	startLon := utils.ToFloat64(req.StartAddr.Lon)
	endLat := utils.ToFloat64(req.EndAddr.Lat)
	endLon := utils.ToFloat64(req.EndAddr.Lon)
	params := map[string]interface{}{
		"action":     action,
		"clientId":   "",
		"systemCode": "62",
		"ticket":     req.Ticket,
		"token":      req.Token,
		"version":    AppVersion,
		"adCode":     req.StartAddr.AdCode,
		"cityCode":   req.StartAddr.CityCode,
		"lat":        req.StartAddr.Lat,
		"lon":        req.StartAddr.Lon,
		"endPoint": map[string]float64{
			"lat": endLat,
			"lon": endLon,
		},
		"passengerJourneyGuid": req.Id,
		"startPoint": map[string]float64{
			"lat": startLat,
			"lon": startLon,
		},
		"sid": req.Sid,
	}
	secretKey := req.SecretKey
	header, data := GetAppSendHeaderAndBody(GetAppParam{
		Action:    action,
		Url:       urlStr,
		Sid:       req.Sid,
		Params:    params,
		SecretKey: secretKey,
	})
	rspBody, _, err := info.Client.Post(urlStr, []byte(data), header)
	if err != nil {
		fmt.Println(err)
		return
	}
	rspBody = DecryptBody(rspBody, secretKey)
	fmt.Println(rspBody)
}

func beforeSubmit(req *dao.SubmitReq, info *CurlInfo) {
	phoneCheck(req, info)
	prePeer(req, info)
	warningBeforeAcceptOrder(req, info)
}

// curlSubmitApp 抢单 APP
func curlSubmitApp(req *dao.SubmitReq, info *CurlInfo) (string, http.Header, error) {
	urlStr := "https://taxiapi.hellobike.com/api"
	action := "hitch.driver.receiveOrder"
	body := GetSubmitBodyApp(req)
	reqHeader, reqBody := GetAppSendHeaderAndBody(GetAppParam{
		Action: action,
		Url:    urlStr,
		Sid:    req.Sid,
		Params: body,

		SecretKey: info.SecretKey,
	})

	bodyStr, rspHeader, err := info.Client.Post(urlStr, []byte(reqBody), reqHeader)
	if err != nil {
		return "", nil, err
	}
	bodyStr = DecryptBody(bodyStr, req.SecretKey)
	return bodyStr, rspHeader, err
}

// curlSubmitWx 抢单 微信
func curlSubmitWx(req *dao.SubmitReq, info *CurlInfo) (string, http.Header, error) {
	bodyByte, _ := json.Marshal(GetSubmitBodyWX(req))
	urlStr := "https://taxiapi.hellobike.com/api?hitch.driver.receiveOrder"
	header := map[string]string{
		"Host":            "taxiapi.hellobike.com",
		"Connection":      "keep-alive",
		"content-type":    "application/json",
		"Accept-Encoding": "gzip,compress,br,deflate",
		"User-Agent":      WxUserAgent,
	}
	bodyStr, rspHeader, err := info.Client.Post(urlStr, bodyByte, header)
	if err != nil {
		return "", nil, err
	}
	return bodyStr, rspHeader, err
}

// GetJourneyList 获取行程列表 小程序
func GetJourneyList(client *utils.HTTPClient, token string, lat, lng string) ([]*dao.HaluoJourneyRspItem, error) {
	urlStr := "https://taxiapi.hellobike.com/api?hitch.core.checkJourneyListV2"
	param := GetJourneyListBody(token, lat, lng)
	header := map[string]string{
		"User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x1800312d) NetType/WIFI Language/zh_CN",
	}

	bodyByte, _ := json.Marshal(param)
	rspBody, _, err := client.Post(urlStr, bodyByte, header)
	if err != nil {
		return nil, err
	}

	var rsp dao.HaluoJourneyRsp
	err = json.Unmarshal([]byte(rspBody), &rsp)
	if err != nil {
		return nil, err
	}
	if rsp.Code != 0 {
		return nil, errors.New(rsp.Msg)
	}
	return rsp.Data, nil
}

// PublishJourneyWxCurl 创建一个行程
func PublishJourneyWxCurl(client *utils.HTTPClient, token string, startTime time.Time, startAddr, endAddr *dao.HaluoRspAddr) (string, error) {
	urlStr := "https://taxiapi.hellobike.com/api?hitch.driver.publishJourney"
	header := map[string]string{
		"User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x1800312d) NetType/WIFI Language/zh_CN",
	}
	tempStartTime := startTime
	for i := 0; i < 3; i++ {
		param := GetPublishJourneyBody(token, tempStartTime, startAddr, endAddr)
		bodyByte, _ := json.Marshal(param)
		rspBody, _, err := client.Post(urlStr, bodyByte, header)
		if err != nil {
			return "", err
		}
		var rsp dao.HaluoPublishRsp
		err = json.Unmarshal([]byte(rspBody), &rsp)
		if err == nil && rsp.Code == 0 {
			return rsp.Data.DriverJourneyGuid, nil
		}
		fmt.Println("创建行程失败", rspBody)
		if rsp.Code == 1211 {
			tempStartTime = time.Unix(tempStartTime.Unix()+(5*60), 0)
		}
	}
	return "", errors.New("创建行程失败")
}

// CancelJourneyWxCurl 删除行程，针对室内单和跨城单
func CancelJourneyWxCurl(req *dao.SubmitReq) {
	if req.ReqType == ReqType1 || len(req.DriverJourneyGuid) <= 0 {
		// 行程单、或者没有行程就不取消
		return
	}
	go func() {
		client := utils.NewHTTPClient(utils.GetProxyIpHttps())
		urlStr := "https://taxiapi.hellobike.com/api?hitch.driver.cancelOrder"
		bodyByte, _ := json.Marshal(GetCancelJourneyBody(req.Ticket, req.Token, req.DriverJourneyGuid))
		header := GetCancelJourneyHeader()
		_, _, _ = client.Post(urlStr, bodyByte, header)
	}()
}

// JourneyAutoReceiveRule 行程单自动抢单规则
func JourneyAutoReceiveRule(client *utils.HTTPClient, id, token, sid string, startTime time.Time, startAddr *dao.HaluoRspAddr) (string, error) {
	urlStr := "https://taxiapi.hellobike.com/api"
	action := "hitch.AutoReceiveOrderPage.PageInitFunction"
	param := GetAutoReceiveRuleBody(id, token, sid, startTime.Unix(), startAddr)
	reqHeader, reqBody := GetAppSendHeaderAndBody(GetAppParam{
		Action:    action,
		Url:       urlStr,
		Sid:       sid,
		Params:    param,
		SecretKey: nil,
	})
	rspBody, _, err := client.Post(urlStr, []byte(reqBody), reqHeader)
	if err != nil {
		return "", err
	}
	body := DecryptBody(rspBody, nil)
	return body, nil
}

// JourneyAutoReceive 行程单自动抢单
func JourneyAutoReceive(client *utils.HTTPClient, id string, startTime time.Time, req *dao.SubmitReq) (string, error) {
	token := req.Ticket + TKSep + req.Token
	startAddr := req.StartAddr
	// 获取接单规则
	ruleBodyStr, err := JourneyAutoReceiveRule(client, id, token, req.Sid, startTime, startAddr)
	var param map[string]interface{}
	if err != nil {
		param = GetAutoReceiveBodyDefault(id, token, req.Sid, startAddr)
	} else {
		var rule dao.HaluoAutoReceiveRuleRsp
		err = json.Unmarshal([]byte(ruleBodyStr), &rule)
		if err != nil {
			param = GetAutoReceiveBodyDefault(id, token, req.Sid, startAddr)
		} else {
			param = GetAutoReceiveBodyAuto(id, token, req.Sid, rule.Data, startAddr)
		}
	}
	urlStr := "https://taxiapi.hellobike.com/api"
	action := "hitch.AutoReceiveOrderPage.AutoReceiveOpen"

	reqHeader, reqBody := GetAppSendHeaderAndBody(GetAppParam{
		Action:    action,
		Url:       urlStr,
		Sid:       req.Sid,
		Params:    param,
		SecretKey: req.SecretKey,
	})
	rspBody, _, err := client.Post(urlStr, []byte(reqBody), reqHeader)
	if err != nil {
		return "", err
	}

	body := DecryptBody(rspBody, req.SecretKey)
	fmt.Println("行程单自动接单", body)
	return body, nil
}

// GetUserInfo 获取司机信息
func GetUserInfo(token string) (*dao.UserInfo, error) {
	client := utils.NewHTTPClient(utils.GetProxyIpHttps())
	urlStr := "https://hitchregistergw.hellobike.com/api?register.person.vehicles"
	reqBody := GetUserInfoBody(token)
	reqBodyByte, _ := json.Marshal(reqBody)
	header := GetUserInfoHeader()
	rsp, _, err := client.Post(urlStr, reqBodyByte, header)
	if err != nil {
		fmt.Println("请求司机信息错误", err)
		return nil, err
	}
	var rspBody map[string]interface{}
	err = json.Unmarshal([]byte(rsp), &rspBody)
	if err != nil {
		fmt.Println("解析司机信息响应体错误", rsp, err)
		return nil, err
	}
	fmt.Println("司机信息", rsp)
	code := rspBody["code"].(float64)
	if int(code) != 0 {
		fmt.Println("获取司机信息错误1", rsp)
		return nil, errors.New("获取司机信息错误")
	}
	data, ok := rspBody["data"].(map[string]interface{})
	if !ok {
		fmt.Println("获取司机信息错误2", rsp)
		return nil, errors.New("获取司机信息错误")
	}
	allVehicles, ok := data["allVehicles"].([]interface{})
	if !ok {
		fmt.Println("获取司机信息错误3", rsp)
		return nil, errors.New("获取司机信息错误")
	}
	baseData, ok := allVehicles[0].(map[string]interface{})
	if !ok {
		fmt.Println("获取司机信息错误4", rsp)
		return nil, errors.New("获取司机信息错误")
	}
	carNum, ok := baseData["vehicleLicensePlateNum"].(string)
	if !ok {
		fmt.Println("获取司机信息错误5", rsp)
		return nil, errors.New("获取司机信息错误")
	}
	driverName, _ := baseData["driverLicenseName"].(string)
	return &dao.UserInfo{
		CarNum:     carNum,
		DriverName: driverName,
	}, nil
}

// GetDetail 获取订单详情
func GetDetail(ticket, token, id, jId string, client *utils.HTTPClient) string {
	urlStr := "https://taxiapi.hellobike.com/api?hitch.driver.journeyDetail"
	reqBody := GetDetailBody(ticket, token, id, jId)
	reqBodyByte, _ := json.Marshal(reqBody)
	header := GetDetailHeader()
	rsp, _, _ := client.Post(urlStr, reqBodyByte, header)
	return rsp
}

// GetDetailV2App 获取订单详情
func GetDetailV2App(client *utils.HTTPClient, req *dao.SubmitReq) string {
	urlStr := "https://taxiapi.hellobike.com/api"
	param := GetDetailV2Param(req.Ticket, req.Token, req.Id, req.StartAddr)
	action := param["action"].(string)

	reqHeader, reqBody := GetAppSendHeaderAndBody(GetAppParam{
		Action:    action,
		Url:       urlStr,
		Sid:       "",
		Params:    param,
		SecretKey: req.SecretKey,
	})

	rspBody, _, err := client.Post(urlStr, []byte(reqBody), reqHeader)
	if err != nil {
		fmt.Println("请求订单详情失败", err)
		return ""
	}
	rspBody = DecryptBody(rspBody, req.SecretKey)
	return rspBody
}

// GetCheckList 获取司机行程列表，包括创建的待出行程、和抢到的订单的行程
func GetCheckList(ticket, token string, client *utils.HTTPClient) string {
	urlStr := "https://taxiapi.hellobike.com/api?hitch.core.checkJourneyListV2"
	reqBody := GetCheckListBody(ticket, token)
	reqBodyByte, _ := json.Marshal(reqBody)
	header := GetCheckListHeader()
	rsp, _, err := client.Post(urlStr, reqBodyByte, header)
	if err != nil {
		return ""
	}
	return rsp
}

// GetCapId 获取滑块验证码信息
func GetCapId(client *utils.HTTPClient, ticket, token string, reqId string, sid string, secretKey *dao.HaluoSecretKey) (*dao.HaluoCapRsp, error) {
	urlStr := "https://api.hellobike.com/api"
	param := GetCapIdParam(ticket, token, reqId, sid)
	action := param["action"].(string)
	reqHeader, reqBody := GetAppSendHeaderAndBody(GetAppParam{
		Action:    action,
		Url:       urlStr,
		Sid:       sid,
		Params:    param,
		SecretKey: secretKey,
	})

	rsp, _, err := client.Post(urlStr, []byte(reqBody), reqHeader)
	if err != nil {
		fmt.Println("请求CapId失败", err)
		return nil, err
	}
	rspAes := DecryptBody(rsp, secretKey)
	var rspCap dao.HaluoCapRsp
	_ = json.Unmarshal([]byte(rspAes), &rspCap)
	fmt.Println("验证码信息", rspAes)
	return &rspCap, nil
}

// GetJySlide 请求跳过滑块验证码
func GetJySlide(client *utils.HTTPClient, captchaId string) (*dao.CapResult, error) {
	payload := map[string]string{
		"captcha_id": captchaId,
		"proxy":      "",
	}
	data, _ := json.Marshal(payload)

	headers := map[string]string{
		"Content-Type": "application/json",
	}
	urlStr := "http://m.loii.top:20000/SlideApi/JySlide/all"
	rsp, _, err := client.Post(urlStr, data, headers)
	if err != nil {
		fmt.Println("请求图片验证失败", err)
		return nil, err
	}
	//{"status": "success", "data": {"lot_number":"d78d22e7bad143fe8ef65640015aa5e5","result":"success","fail_count":0,"seccode":{"captcha_id":"fd462d7fa7f47be21fc2b675f741aa37","lot_number":"d78d22e7bad143fe8ef65640015aa5e5","pass_token":"b6f3d7dc850d2bff045424c80d9a7d80b0b3a6b88479e293309a8e81e1b0b8e1","gen_time":"1731318990","captcha_output":"bIxtR0o_OLOXE5PG-ljBO0vzDTX7heJs-1bbxwR9b8WeA3pf_Ax9Pi8-GC1Ilfu30mqh5o_PtPGGpi8TMZETB-r_y3_rXOPhiP7spQm4wDKLdAyQa3slSoo92RmruKy5e11K0adFNuMU8bEm6mMEtr7AjRGZh_UKrdLQELg0bLjgexbMBIWhK267W6iDnqDALfn55U9ghRsiGewCtXEjFmNiaLFt8IXqnPaxY_3rOLX_dQ9mTXvHTfX2s7ezt8HAj16QooFzleApiARkmagg8vfa3_CHG6l7Ma34yG0C6jc8m58GYxPXqBsnk77JYlbFu5n1KnJsqpaBz-JP1HZOaGTj5Jt1jZdSkkC-9J25Xs1DUI-QISsvSVD2Pjqx7b0aW-tsmsTsHz1WlPgpY4wZ1CU-UNnOm8pK8saE8XK4jIc4j7pgV1mqa4VwPD_Z6D5KRefO97FONVpJeN_3aG3ttkrHe4A7FAA_aaA2zkPiERXmunt6ZzCB5loMs12xEhb5mW8GOLRa5sKgksVvMMlWamyz0waH170DWrTknPjaTl_bx9C8yN0ixV84j334gkel_1zwpO57EBhKaDUpECyOq-Zg58Poler4iBp1JwLMjBg="},"score":"1","payload":"AgFD8gWUUuHFx-XvpP7J2SOP9gf19F1Bo6Xs05cIhTxCZpht9kLaq8qwVpF5qTns_00wwtF1-0r-f-JaAmhhmCWzYwWIAUnfStulxPJtOgpVLBj6hegbJYDk35KI-QEwxZ5MZXM-pSGPqolrut6LQLi3tizErq_MUC4zdkxTINzywC1V8R3qA_BDhcDUDxBbaaR9CRzUAqpL3ECWB84dMfRf0rEaWTjvdjSAX_IkFZjQeYaEH9nemjuILdPNvn05gIFYFkG-GFsXGS27_xBVPyr2JroPwcqxmNiFdKVN7pjiOiZJ8JiqTXwrnCfJCGbyqhqesByWYzEwv-CZFPVGNl3ex1YF2w5-OvoLwHfhNPLqaX9EvOxxpQR8R8leMKfvuzEX6wQ7MtEVag1bzqkKtXT9UeA2DiS2khSHUA20ItgBmCyxAOrFskFxMIeQrJkWkwrwk24muwV0dS5ccH3QtVWgJi04TOUhLkX65-5unB3wmDQo5wiEiqqHEFjKHiJh_-f5Ojabkpzs0bBIdPef67Di7RXFurX6_Bd1JMZNaOKuoZByyUHUNzZQ3T8hcLjorDuEk6BxqpWQdmLjcCLgBgoz4dVBcqJmZFr4AD0DDlgxdTHfur_2cx9O2t9PUqVltb4pR55km0X_ofdUr4AE0ITOYGI1YN2wBCiEBI9vpJS4wuGbcKWsV5jOGrNAF6f_M1xSJuwZUNHq9uLc3PU4BXLykx7GaQVMPV89IOCnenbWSEZQ8isrYhD2RWMD5fua8OA9rWj80dMwA4p_zQW-dghqB-ZWDbbWGSl719qYOUVYSUH-RhE6fLHjZXAK26n4g0Xjh21OA6KPyoPwnBgbkyHklzk0JPtNtVDIl36doAEH_obwZOs4D6ItWvVPfaX_yrzvdPX2i52-CVZ9cPu1aO37JzKTINHpAFufqq7XRYg855qrnsYI-J5F1YBzxXHRE5KBxw9DdiOM0Kmu9HKFm3G4bc5uPq45OuqtLWwwMTQU5GLBF2WmKzilgbWBu7PbXDlzObQUOP_JpbrBS3OB31e3k-YPeqAtXEoaM7mtrMO-WMyBjzUeBL405xPPE57he9zAK8MSAT0LIpy7XbQCXTetMlYdBXnAdjQXJ89fuscTgblfcN20jvExvf5umptnTebb63ORLCCVtgADgzQsck1_QW2wLV2XDiyJ5TYrpKFYScsWrDxsJVjdbIw4ndyaod6FKdhYwCdaaPkSEmU-zp5C2Vd2kS93ugqYm2rhVd5I4N8p0gl6krEmcEeetZscxwGc7qqzWBGwOIH7YcDTK7W1IApRQnueQeY0YjjIL5MpZFesBbjnmFulLFgyHCSbgwo33k60ehMlAD6K432KpTS8-xTza3zLlwikODPANY9uQkvxj4z0E3m52YIiL1OHJ1s30A9Hw3kuw20BUwdcXbCVny5WNBakkx9uEMembEmPqAwre85ebS4V6jbXKH3dQnFv7Hrz6x52xaxB_27uck6Uuiqmyc-H3jXtyWnJb-fZZtDKsH2-R94pAChO30JvC0kzwmPVihdt6p8C_w3QtZ6mEXNFUykycu5q0tZ29-xhfEBaYxrrpn3v7in3dSIBLeaA9hoQylmLvxS1O69W91X9uPA393WGBm_cSg2XqjJxEL2-4yR9FQHHNwM3BE_RGHHN4OVbhOADL-iuhpSboHDHsZNMd9JvdS4_8BycxTeRXL-HrZzl1RhGeA3gAufYdMUajSEB7uZ6pXzy8nYAfOIOCliBTNZLQmCMYzvPha_0nvk4J4JOuDl5mBiMzNE5-n79Uk9wcnBJqjW6FLCIdZw4dyMC3Z9clrcHfGhDtulJCv9O_mBZq0rRjoke8DnwdNQ5EijSK4dCsJ5C6UErqQTA6RCy7UF7hoROFzshqQxqXOyX3zXyLh5hSCoByoebX2xOjcSXPkmiGtVXx5jd-zOu6ihxIfReS4vE-R7LzYK7qduhBYXyjxgSvNzQn5mCYALUtRtZGRqAAfFinZHqNIadXANdvpRopcOI6qnGJ-Y9ra-n613_e7VSVqpR1TsCrs_gc7tkhdmkywE5kxEZ8sxT3PBRhsdANH-W7zfUFMEdNE39jhNh29W3C4vd4mwuGyyI5Stv23kOGobzDTkoZsEW3Gl-UTTIqgOH1VMSUke2La0aTLIzswoLy-bU3nsvmVMw8ehErURL8aGhuug8M6VRXB9pPNyNrCaXZwSD4eQtilVIgP_Q1wWcCLrcUHe-P60F6218_4zK7HegYjT7M9J2gOz0uMZ7buCPcnpG0kuZZ7-aI7QpuqU2un95_cCjT9VUTXujkWjMicgnjj-5R4eN15UZKQuNE_HsqqlO1C4c23bXYqaVxFDyk4e1blFuvjINnTIF68Fszvqqb84IbuL_XGnlXxU3zU6tTxm4R6fweCqHn0EYk5IDU_VP9K9A6l8C3dHxVQ01LyFoLXKFCG2vSC4-r5PSkk0r4wkehk6PS2iosLDvvwBMKGRruooAM3g_lOYyr3u3YpW-DIIVt0EXyQ86iOa4Q7oaOnB1lROH0QVsuzwrInG5xOVTpNK_YIM1OGiG-skDpGCC-IwMTwFiZF86iUXAUzNUz-geANZodIHDvIrhG8eDr51SJvyqAvANnIgeNrxfBAD89IAE9x29SZUTsdY4-NQE-I-u6k095qr7OcxFoZjBfPppRWaazNyxO1grr4ek7y4_DzVeyIfLlnqbPG2KuV8AEA-Fa0YeqKmiC95d9SCH2AXESksLeK_1Sap1qArwsCItCFxaQfos1cQ9NTrGe4tzAu4Q55psaZa3vqB8dXMDOYS4irTK8bgee9Uv1Mkku0c0-rL3TvH5HiFFmyQ86eD0aAF8z4jytkbzCP0F9bbhLUt_i8DExcEZ8Z4rDU1zTHglpzWjsWR3_Jk2c22bTjpy4L-fmROyS3QUu4Y6E4o8AvgrXH9K3H6-oHUI8tdedTuxA1utrIh-Hiq2abu4EAVRT0TPn2CaUhRkvAKtVbT_pWvbap6yE_arWy9EYeEmrol5oGjVmwEdMoolO51KEeqYR2EOz0f10n0gXx6B05nH3KA2OSYMiqJqyUUt3EnVG_O0pVOC20hY9_0BJ78eyHP_WSNt3gH-G4prXO02qg_AvWYemX8RZcRmGqf9-FvjEsnmBmSCNGtIDFp46V7-DLE53f6FAet8VX14oP3OlDi_nEeP6JipSVkwAXITopCSF1OEu_0r6_vLb58Kta_HAO05ME7YFxalOeeE7pbro25YrYRA-6jAyarlRSooUFzrUGAj5WzKfqbLtEk-QqgBG3UjIR9ExD6F-m3OXf-wzzcP2-ZkH7Edd1-wCCsICHi0gt4PAEhFtV5hxSN1Fts_bVvG3bymecUqKZDsMm0NWwxowXXV9uo5z8_z5dvaWp32wBkdJ9UMe4822zkfE7Pqarxe7fwi5ta4u5JX4wJ2pLDeerhne7V66tunEzbRPEfPzBUof_p9mQ==","process_token":"cc197f1da388c348b0d91e95a1a37db9079f9e6740d30726a98894db1b430389","payload_protocol":1}}
	fmt.Println(rsp)
	var rspRes dao.CapResult
	_ = json.Unmarshal([]byte(rsp), &rspRes)
	return &rspRes, nil
}

// CheckCaptcha 将滑块验证信息提交给哈啰
func CheckCaptcha(client *utils.HTTPClient, ticket, token string, reqId string, sid string, secretKey *dao.HaluoSecretKey, rspCapR *dao.CapResult, rspCap *dao.HaluoCapRsp) (*dao.HaluoCheckCapRsp, error) {
	urlStr := "https://api.hellobike.com/api"
	param := GetCheckCaptchaParam(ticket, token, reqId, sid, rspCapR, rspCap)
	action := param["action"].(string)

	reqHeader, reqBody := GetAppSendHeaderAndBody(GetAppParam{
		Action:    action,
		Url:       urlStr,
		Sid:       sid,
		Params:    param,
		SecretKey: secretKey,
	})

	rsp, _, err := client.Post(urlStr, []byte(reqBody), reqHeader)
	if err != nil {
		fmt.Println("提交滑块完成信息", err)
		return nil, err
	}
	rspAes := DecryptBody(rsp, secretKey)

	// {"code":0,"msg":"ok","data":{"success":0,"msg":"Please verify"}}
	// {"code":0,"msg":"ok","data":{"success":1,"msg":"success"}}
	var rspCheck dao.HaluoCheckCapRsp
	_ = json.Unmarshal([]byte(rspAes), &rspCap)
	fmt.Println("验证码信息", rspAes)
	return &rspCheck, nil
}
