package dao

import "time"

type GradReq struct {
	Phone         string
	GToken        string
	FToken        string
	GTokenFresh   bool
	OrderBy       int
	CarNum        string
	DRute         *GradReqDRute
	BRute         *GradReqBRute
	CRute         *GradReqCRute
	UserInfo      *UserInfo
	StartTime     time.Time
	IsPass12Check bool
	SecretKey     *HaluoSecretKey
	SidMap        map[string]string
}

type GradReqDRute struct {
	RouteIds map[string]*GradReqRouteInfo
	ShunLu   int
	OrderBy  int
	FromDist int
	ToDist   int
	FreshMin int
	FreshMax int
}

type GradReqBRute struct {
	TabIds    []int
	FromDist  int
	ToDist    int
	OrderBy   int
	OrderDist int
	NeedCity  []string
	NotCity   []string
	FreshMin  int
	FreshMax  int
}

type GradReqCRute struct {
	ApiType       int // 0:APP 1:小程序
	FInterval     int
	FIntervalStop int
	OrderType     int
	HighWay       bool
	Date          int
	StartH        int
	EndH          int
	MoneyMin      int
	MoneyMax      int
	Oneself       bool
	OneselfMin    int
	OneselfMax    int
	Pinzuo        bool
	PinzuoMin     int
	PinzuoMax     int
	Shushi        bool
	ShushiMin     int
	ShushiMax     int
	AdCode        string
	Lat           string
	Lng           string
}
type HaluoSecretKey struct {
	Key             string `json:"key"`
	FingerprintHash string `json:"fingerprint-hash"`
	Expired         int    `json:"expired"`
	KeyVersion      string `json:"key-version"`
	HelloToken      string `json:"helloToken"`
}

type UserInfo struct {
	CarNum     string
	DriverName string
}

type GradReqRouteInfo struct {
	Id       string `json:"id"`
	AdCode   string `json:"adCode"`
	CityCode string `json:"cityCode"`
	Time     int64  `json:"time"`
}

type GradReqCity struct {
	AdCode   string
	CityCode string
	Lat      string
	Lng      string
}

type SubmitReq struct {
	UserInfo          *UserInfo
	Phone             string
	ApiType           int
	ReqType           int
	Ticket            string
	Token             string
	Sid               string
	SecretKey         *HaluoSecretKey
	Id                string        // 订单ID
	ArriveTime        int64         // 出发时间
	ProductCode       int           // 产品类型，特惠订单才有
	StartAddr         *HaluoRspAddr // 起点地址
	EndAddr           *HaluoRspAddr // 重点地址
	DriverJourneyGuid string        // 行程单ID
	OrderDesc         string
	OrderFromName     string
	OrderToName       string
	OrderFromDist     string
}

const (
	WsDataTypeConsole       = 1
	WsDataTypeRecord        = 2
	WsDataTypeErr           = 10
	WsDataTypeSuccess       = 11
	WsDataTypeNoticeSuccess = 20
	WsDataTypeNoticeErr     = 21
	WsDataTypeNoticePK      = 22

	WsConsoleTypeLog  = 1
	WsConsoleTypeInfo = 2
	WsConsoleTypeErr  = 3
	WsConsoleTypeWarn = 4
)

type WsData struct {
	Type int         `json:"type"`
	Body interface{} `json:"body"`
}

type WsBodyConsole struct {
	Data []WsBodyConsoleData `json:"data"`
}
type WsBodyConsoleData struct {
	Type int    `json:"type"`
	Msg  string `json:"msg"`
}

func BuildWsConsole(data []WsBodyConsoleData) *WsData {
	return &WsData{
		Type: WsDataTypeConsole,
		Body: WsBodyConsole{
			Data: data,
		},
	}
}

func BuildWsRecord(msg []string) *WsData {
	return &WsData{
		Type: WsDataTypeRecord,
		Body: msg,
	}
}
