package gaode

import (
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitzhang87/shenhouS/internal/utils"
	"net/url"
	"strconv"
	"strings"
)

const (
	AppKey    = "fcf51f78ff7942bef0159f113eaad7e2"
	AppSecret = "1ca8d1d809a9407c3bb9eeda627105c3"
)

func Geo(addr string) (map[string]string, error) {
	// 请求高德接口
	baseUrl := "https://restapi.amap.com/v3/geocode/geo?"
	addr = url.QueryEscape(addr)
	params := fmt.Sprintf("address=%s&key=%s", addr, AppKey)

	sigParam := params + AppSecret
	sig := md5.Sum([]byte(sigParam))
	baseUrl = baseUrl + params + "&sig=" + fmt.Sprintf("%x", sig)

	client := utils.NewHTTPClient("")
	body, _, err := client.Get(baseUrl, nil)
	if err != nil {
		return nil, errors.New("高德获取经纬度异常")
	}
	var rsp *GeoRsp
	err = json.Unmarshal([]byte(body), &rsp)
	if err != nil {
		fmt.Println(err)
		return nil, errors.New("高德获取经纬度异常1")
	}
	count, _ := strconv.Atoi(rsp.Count)
	if rsp.Status != "1" || count <= 0 {
		return nil, errors.New("地址错误")
	}

	geoInfo := strings.Split(rsp.Geocodes[0].Location, ",") // 110.210967,20.011668
	if len(geoInfo) != 2 {
		return nil, errors.New("地址错误")
	}
	return map[string]string{
		"lng": geoInfo[0],
		"lat": geoInfo[1],
	}, nil
}
