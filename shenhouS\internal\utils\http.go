package utils

import (
	"bytes"
	"compress/gzip"
	"crypto/tls"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// HTTPClient struct to hold the base URL and an HTTP client
type HTTPClient struct {
	Client *http.Client
}

// NewHTTPClient function to initialize a new HTTP client
func NewHTTPClient(proxyUrl string) *HTTPClient {
	return &HTTPClient{
		Client: &http.Client{
			Timeout:   time.Second * 100,
			Transport: getTr(proxyUrl),
		},
	}
}

func (c *HTTPClient) ChangeProxy(proxyUrl string) {
	c.Client = &http.Client{
		Timeout:   time.Second * 5,
		Transport: getTr(proxyUrl),
	}
}

func getTr(proxyUrl string) *http.Transport {
	var tr *http.Transport
	if len(proxyUrl) != 0 {
		proxy, _ := url.Parse(proxyUrl)
		tr = &http.Transport{
			Proxy:           http.ProxyURL(proxy),
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
	} else {
		tr = &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
	}
	return tr
}

// Get method to send a GET request
func (c *HTTPClient) Get(url string, headers map[string]string) (string, http.Header, error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", nil, err
	}
	for key, value := range headers {
		req.Header.Set(key, value)
	}
	resp, err := c.Client.Do(req)
	if err != nil {
		return "", nil, err
	}
	defer func() {
		_ = resp.Body.Close()
	}()

	var body []byte
	if resp.Header.Get("Content-Encoding") == "gzip" {
		var r *gzip.Reader
		r, err = gzip.NewReader(resp.Body)
		if err != nil {
			return "", nil, err
		}
		defer func() {
			_ = r.Close()
		}()

		// 读取解压后的数据
		body, err = io.ReadAll(r)
	} else {
		body, err = io.ReadAll(resp.Body)
	}
	if err != nil {
		return "", nil, err
	}
	return string(body), resp.Header, nil
}

// Post method to send a POST request
func (c *HTTPClient) Post(url string, data []byte, headers map[string]string) (string, http.Header, error) {
	postData := bytes.NewBuffer(data)
	req, err := http.NewRequest("POST", url, postData)
	if err != nil {
		return "", nil, err
	}
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := c.Client.Do(req)
	if err != nil {
		return "", nil, err
	}
	defer func() {
		_ = resp.Body.Close()
	}()

	var body []byte
	if strings.Contains(resp.Header.Get("Content-Encoding"), "gzip") || strings.Contains(resp.Header.Get("Accept-Encoding"), "gzip") {
		var r *gzip.Reader
		r, err = gzip.NewReader(resp.Body)
		if err != nil {
			return "", nil, err
		}
		defer func() {
			_ = r.Close()
		}()
		// 读取解压后的数据
		body, err = io.ReadAll(r)
	} else {
		body, err = io.ReadAll(resp.Body)
	}
	if err != nil {
		return "", nil, err
	}
	return string(body), resp.Header, nil
}

func GetProxyIpHttps() string {
	if IsDebug() {
		return "http://127.0.0.1:9999"
	}
	return ""
}
