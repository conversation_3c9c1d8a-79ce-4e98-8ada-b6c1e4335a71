package grab

import (
	"encoding/json"
	"fmt"
	"github.com/limitzhang87/shenhouS/internal/dao"
	"github.com/limitzhang87/shenhouS/internal/module/pjy"
	"github.com/limitzhang87/shenhouS/internal/utils"
	"github.com/limitzhang87/shenhouS/internal/websocket"
	"strings"
	"sync"
	"time"
)

// 全局token缓存
var (
	tokenCache = make(map[string]string) // oldToken -> newToken
	tokenMutex sync.RWMutex
)

type GradHandler struct {
	Ws      *websocket.Handler
	Req     *dao.GradReq
	HadGet  map[string]struct{}
	BadTK   map[string]string
	PJYCode string
	PJY     *pjy.PJY
}

func NewGradHandler(ws *websocket.Handler, req *dao.GradReq, code string) *GradHandler {
	return &GradHandler{
		Ws:      ws,
		Req:     req,
		HadGet:  make(map[string]struct{}),
		BadTK:   make(map[string]string),
		PJYCode: code,
		PJY:     pjy.NewPJY(pjy.AppKey, pjy.AppSecret),
	}
}

func (h *GradHandler) Run() {
	defer func() {
		if r := recover(); r != nil {
			fmt.Println("Recovered from panic:", r)
			h.StopErr("服务器异常" + fmt.Sprint(r))
		}
	}()

	// 卡密检查
	if h.WaitCheckPJY() == false {
		h.StopErr("")
		return
	}
	h.MultiCheckPJY()

	// 获取密钥
	h.Req.SecretKey, _ = GetSecretKey()
	h.Req.SidMap = GetSidMap(h.Req)

	dCurlInfos := GetCurlInfoD(h.Req)
	bCurlInfos := GetCurlInfoB(h.Req)

	driverJourneyIdMap := h.GetDriverJourneyIdForFTK(dCurlInfos)
	h.CancelJourneyForFTK(driverJourneyIdMap) // 任务结束之后删除刷新TK创建的行程

	allCurlInfos := append([]*CurlInfo{}, dCurlInfos...)
	allCurlInfos = append(allCurlInfos, bCurlInfos...)

	lessTime := 0
	h.Req.BRute.FreshMin += lessTime
	h.Req.BRute.FreshMax += lessTime
	h.Req.DRute.FreshMin += lessTime
	h.Req.DRute.FreshMax += lessTime

	fInterval := h.Req.CRute.FInterval
	if fInterval == 0 {
		fInterval = 100
	}
	fIntervalStop := h.Req.CRute.FIntervalStop
	if fIntervalStop == 0 {
		fIntervalStop = 0
	}

	if len(dCurlInfos) == 0 && len(bCurlInfos) == 0 {
		h.StopErr("无抢单任务")
		return
	}

	h.log([]string{utils.PrintTime(time.Time{}) + " 任务启动"})
	req := h.Req
	//dFresh := 50 // 行程单刷新速率
	bFresh := 50 // 大厅单刷新速率
	h.log([]string{utils.PrintTime(time.Time{}) + " 任务创建成功，开始抢单"})
	var wg sync.WaitGroup
	if len(allCurlInfos) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			dIndex := 0
			fIntervalTicker := time.NewTicker(time.Second * time.Duration(fInterval))
			for {
				timeout := time.After(time.Millisecond * time.Duration(bFresh))
				select {
				case <-h.Ws.CheckStop():
					fIntervalTicker.Stop()
					return
				case <-fIntervalTicker.C:
					if fIntervalStop > 0 {
						msg := fmt.Sprintf("防封暂停%d秒", fIntervalStop)
						fmt.Println(msg)
						h.log([]string{msg})
						time.Sleep(time.Second * time.Duration(fIntervalStop))
					}
				case <-timeout:
					bFresh = utils.GetFresh(req.BRute.FreshMin, req.BRute.FreshMax)
					// 检查过了12点之后日期要改变
					curlInfo := allCurlInfos[dIndex]
					if !req.IsPass12Check {
						now := time.Now().Format(time.DateOnly)
						sDate := req.StartTime.Format(time.DateOnly)
						if now != sDate {
							// 日期不相等，过了12点了
							if req.CRute.Date > 1 {
								req.CRute.Date -= 1
								allCurlInfos = append([]*CurlInfo{}, GetCurlInfoD(h.Req)...)
								allCurlInfos = append(allCurlInfos, GetCurlInfoB(h.Req)...)
								req.IsPass12Check = true
								dIndex = 0
								tt, _ := json.Marshal(req.CRute)
								fmt.Println("过了12点替换", string(tt))
								continue
							}
						}
					}
					ok := h.exec(curlInfo)
					if ok {
						h.StopSuccess()
						return
					}
				}
				dIndex++
				dIndex = dIndex % len(allCurlInfos)
			}
		}()
	}
	wg.Wait()
	h.Ws.Stop()
}

// WaitCheckPJY 启动前检测卡密
func (h *GradHandler) WaitCheckPJY() bool {
	// 检查卡密
	h.log([]string{"正在检查卡密中..."})
	time.Sleep(4 * time.Second)

	if !h.PJY.CheckConfigErr(h.PJYCode, h.Req.UserInfo.CarNum) {
		//发送失败信息
		//h.StopErr("禁止购买和使用盗版，请加正版QQ：997888241")
		h.StopErr("检查卡密是否正确")
		return false
	}
	h.log([]string{"卡密检查正确"})
	return true
}

// MultiCheckPJY 循环检测卡密
func (h *GradHandler) MultiCheckPJY() {
	// 每隔10分钟检测gvid
	s := 15
	go func() {
		for {
			after := time.NewTimer(time.Second * time.Duration(s))
			select {
			case <-after.C:
				if !h.PJY.CheckConfigErr(h.PJYCode, h.Req.UserInfo.CarNum) {
					//发送失败信息
					h.StopErr("检查卡密是否正确")
				}
				s *= 2
				if s >= 300 {
					s = 300
				}
			case <-h.Ws.CheckStop():
				return
			}
		}
	}()
}

func (h *GradHandler) GetSid(ticket string) string {
	return h.Req.SidMap[ticket]
}

func (h *GradHandler) exec(info *CurlInfo) bool {
	if t, ok := h.IsBadTK(info.CurToken); ok {
		h.err(t + " TK失效")
		return false
	}
	startTime := time.Now()
	rspStr, _, err := GetList(h.GetSid(info.Ticket), info) // 刷新订单列表
	fmt.Println("获取订单并筛选耗时", time.Now().Sub(startTime).Milliseconds())
	if err != nil {
		return false
	}
	var rsp *dao.HaluoRspList
	_ = json.Unmarshal([]byte(rspStr), &rsp)
	if rsp == nil {
		fmt.Println("解析json", err, rspStr)
		h.err(info.TokenText + " 刷新太快，请休息一下吧")
		return false
	}
	if rsp.Code == 103 {
		h.SetBadTK(info.CurToken, info.TokenText)
		h.err(info.TokenText + " TK失效，尝试自动刷新...")

		// 尝试自动刷新token
		if h.RefreshToken(info) {
			h.err(info.TokenText + " TK刷新成功，继续抢单")
			// 刷新成功后，从BadTK中移除
			delete(h.BadTK, info.CurToken)
			return false // 返回false让下次循环重新尝试
		} else {
			h.err(info.TokenText + " TK刷新失败")
			//h.StopErr("TK失效，请重新登录")
			return false
		}
	}
	if rsp.Code != 0 {
		fmt.Println("请求订单", rspStr)
		if rsp.Code == 101 {
			h.StopErr(info.TokenText + " 当前位置不可用，请更换位置试试")
			return false
		}
		if rsp.Code == 99 {
			h.errs([]string{info.TokenText + " 正在执行滑块验证，请稍等"})
			checkErr := h.Captcha(info, rspStr)
			if checkErr {
				h.err(info.TokenText + " 已完成滑块验证")
			} else {
				h.err(info.TokenText + " 无法完成滑块验证")
			}
			return false
		}
		h.err(info.TokenText + " 哈啰错误 " + rspStr)
		return false
	}
	if rsp.Data == nil {
		h.err(info.TokenText + " 哈啰错误 " + rspStr)
		return false
	}

	// 获取符合条件的订单
	submitReqList := h.GetValidOrder(info, rsp.Data)
	if len(submitReqList) == 0 {
		return false
	}
	recordList := make([]string, 0, len(submitReqList)*6)
	defer func() {
		h.info(recordList)
	}()
	for _, submitReq := range submitReqList {
		h.SetOid(submitReq.Id)
		ok, records := h.submit(submitReq, info)
		if ok {
			return true
		}
		recordList = append(recordList, records...)
		recordList = append(recordList, "-------------------")
	}
	return false
}

func (h *GradHandler) SetOid(oid string) {
	h.HadGet[oid] = struct{}{}
}

func (h *GradHandler) IsOid(oid string) bool {
	_, ok := h.HadGet[oid]
	return ok
}

func (h *GradHandler) SetBadTK(tk, t string) {
	h.BadTK[tk] = t
}

func (h *GradHandler) IsBadTK(tk string) (string, bool) {
	t, ok := h.BadTK[tk]
	return t, ok
}

// RefreshToken 自动刷新token
func (h *GradHandler) RefreshToken(info *CurlInfo) bool {
	// 获取手机号
	phone := h.Req.Phone
	if phone == "" {
		fmt.Println("手机号为空，无法自动刷新token")
		return false
	}

	// 通知前端需要刷新token
	refreshReq := dao.TokenRefreshRequest{
		Phone:     phone,
		TokenText: info.TokenText,
	}
	data := dao.WsData{
		Type: dao.WsDataTypeTokenRefresh,
		Body: refreshReq,
	}
	dataByte, _ := json.Marshal(data)
	h.Ws.Send(dataByte)

	h.log([]string{info.TokenText + " 正在自动刷新token，请稍等..."})

	// 等待前端刷新token，最多等待60秒
	timeout := time.After(60 * time.Second)
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			fmt.Println("token刷新超时")
			return false
		case <-h.Ws.CheckStop():
			return false
		case <-ticker.C:
			// 检查是否有新的token
			if newToken := h.checkNewToken(info.CurToken); newToken != "" {
				// 更新token
				h.updateToken(info.CurToken, newToken)
				return true
			}
		}
	}
}

// checkNewToken 检查是否有新的token
func (h *GradHandler) checkNewToken(oldToken string) string {
	tokenMutex.RLock()
	defer tokenMutex.RUnlock()

	if newToken, exists := tokenCache[oldToken]; exists {
		return newToken
	}
	return ""
}

// SetNewToken 设置新的token到缓存
func SetNewToken(oldToken, newToken string) {
	tokenMutex.Lock()
	defer tokenMutex.Unlock()
	tokenCache[oldToken] = newToken
	fmt.Printf("Token缓存已更新: %s -> %s\n", oldToken[:20]+"...", newToken[:20]+"...")
}

// updateToken 更新token
func (h *GradHandler) updateToken(oldToken, newToken string) {
	// 更新请求中的token
	if h.Req.GToken == oldToken {
		h.Req.GToken = newToken
	}

	// 更新刷新token列表
	for i, token := range h.Req.FToken {
		if token == oldToken {
			h.Req.FToken[i] = newToken
			break
		}
	}

	// 清理token缓存
	ClearTokenCache(oldToken)

	fmt.Printf("Token已更新: %s -> %s\n", oldToken[:20]+"...", newToken[:20]+"...")
}

// ClearTokenCache 清理token缓存
func ClearTokenCache(oldToken string) {
	tokenMutex.Lock()
	defer tokenMutex.Unlock()
	delete(tokenCache, oldToken)
}

// GetValidOrder 根据滴滴返回的数据，筛选出符合的订单
func (h *GradHandler) GetValidOrder(curlInfo *CurlInfo, data *dao.HaluoRspListData) []*dao.SubmitReq {
	if data.List == nil || len(data.List) == 0 {
		fmt.Println("正在搜索订单中")
		h.info([]string{"正在搜索订单中"})
		return nil
	}
	list := data.List
	orderLogList := make([]string, 0, len(list)) // 订单基本信息
	// 行程1， 行程2， 大厅单， 跨城单
	msg := curlInfo.TokenText + " "
	switch curlInfo.ReqType {
	case ReqType1:
		msg += fmt.Sprintf("行程单%s %s - %s", curlInfo.RouteIndex, data.StartAddr.ShortAddr, data.EndAddr.ShortAddr)
	case ReqType2:
		msg += "市内单"
	case ReqType3:
		msg += "城际单"
	}
	fmt.Println(msg)
	orderLogList = append(orderLogList, msg)
	submitList := make([]*dao.SubmitReq, 0)
	defer func() {
		// 发送当天订单列表基本信息
		if err := recover(); err != nil {
			h.err(curlInfo.TokenText + " 订单信息出现异常")
		}
		if len(submitList) <= 0 {
			orderLogList = append(orderLogList, "未找到符合订单")
		} else {
			orderLogList = append(orderLogList, fmt.Sprintf("找到符合条件订单(%d条)", len(submitList)))
		}
		h.info(orderLogList)
	}()
	req := h.Req
	for _, item := range list {
		id := item.Id
		if len(id) == 0 {
			temp, _ := json.Marshal(item)
			fmt.Println("订单信息返回错误", temp)
			h.err("订单信息返回错误")
			continue
		}
		carPoolText := item.CarPoolText
		// 乘客数量
		peopleNum := item.PassengerCount

		// 价格信息
		price := item.Price
		priceFloat := float64(price) / 100.00

		// 里程信息
		distance := float32(item.Distance) / 1000.00

		// 顺路度
		degree := int(item.HitchPercent * 100)

		// 出发时间
		startTime := time.Unix(item.StartPlanStartTime/1000, 0)
		endTime := time.Unix(item.EndPlanStartTime/1000, 0)
		d := utils.GetDateDescription(item.StartPlanStartTime / 1000)
		sH, sM := utils.ToInt(startTime.Format("15")), utils.ToInt(startTime.Format("04"))
		eH, eM := utils.ToInt(endTime.Format("15")), utils.ToInt(endTime.Format("04"))

		// 打印订单基本信息  拼座1人  71元  7km 顺路80% 后天 21:20-21:35
		var recordInfo string
		if curlInfo.ReqType == ReqType1 {
			recordInfo = fmt.Sprintf("%s%d人 %0.2f元 %0.2fkm 顺路%d%% %s %02d:%02d-%02d:%02d", carPoolText, peopleNum, priceFloat, distance, degree, d, sH, sM, eH, eM)
		} else {
			recordInfo = fmt.Sprintf("%s%d人 %0.2f元 %0.2fkm %s %02d:%02d-%02d:%02d", carPoolText, peopleNum, priceFloat, distance, d, sH, sM, eH, eM)
		}
		// 判断是否已经抢过
		if h.IsOid(id) {
			recordInfo += "  PK中"
			fmt.Println(recordInfo)
			orderLogList = append(orderLogList, recordInfo)
			continue
		}
		fmt.Println(recordInfo)
		orderLogList = append(orderLogList, recordInfo)

		if curlInfo.ReqType == ReqType1 {
			//行程单判断起点终点距离
			if req.DRute.FromDist > 0 {
				tempFromDist := utils.ToFloat32(item.StartDistance)
				tempFromDist /= 1000.00
				if tempFromDist == 0 || tempFromDist > float32(req.DRute.FromDist) {
					fmt.Println("起点距离不符合", req.DRute.FromDist, tempFromDist)
					continue
				}
			}
			if req.DRute.ToDist > 0 {
				tempToDist := utils.ToFloat32(item.EndDistance)
				tempToDist /= 1000.00
				if tempToDist == 0 || tempToDist > float32(req.DRute.ToDist) {
					fmt.Println("终点距离不符合", req.DRute.ToDist, tempToDist)
					continue
				}
			}

			// 判断顺路 行程单和常用路线单才判断
			if req.DRute.ShunLu > degree {
				fmt.Println("顺路度不够", req.DRute.ShunLu, degree)
				continue
			}
		}

		// 判断大厅起点距离
		if (curlInfo.ReqType == ReqType2 || curlInfo.ReqType == 3) && req.BRute.FromDist > 0 {
			tempFromDist := utils.ToFloat32(item.StartDistance)
			tempFromDist /= 1000.00
			if tempFromDist == 0 || tempFromDist > float32(req.BRute.FromDist) {
				fmt.Println("起点距离不符合", req.BRute.FromDist, tempFromDist)
				continue
			}
		}

		// 判断只接城市和不接城市
		if curlInfo.ReqType == ReqType3 {
			toName := item.EndAddr.CityName
			tempNeedCity := req.BRute.NeedCity
			// 只接城市
			if len(tempNeedCity) > 0 {
				isIn := false
				for _, city := range tempNeedCity {
					if utils.CharContains(toName, city) {
						isIn = true
						break
					}
				}
				if !isIn {
					fmt.Println("目的地不在只接城市", toName, req.BRute.NeedCity)
					continue
				}
			}
			// 不接城市
			tempNotCity := req.BRute.NotCity
			if len(tempNotCity) > 0 {
				isIn := false
				for _, city := range tempNotCity {
					if len(city) > 0 && utils.CharContains(toName, city) {
						isIn = true
						break
					}
				}
				if isIn {
					fmt.Println("目的地在不接城市", toName, tempNotCity)
					continue
				}
			}
		}

		// 判断日期时间
		//if cRoute.Date != 0 {
		//	if dateList[cRoute.Date] != d {
		//		fmt.Println("日期不符合", dateList[cRoute.Date], startDate)
		//		continue
		//	}
		//}
		// 判断时间
		if req.CRute.StartH > 0 || req.CRute.EndH > 0 {
			if eH < req.CRute.StartH || sH > req.CRute.EndH {
				fmt.Println("时间不符合", req.CRute.StartH, req.CRute.EndH, eH, sH)
				continue
			}
		}

		// 判断金额
		if req.CRute.MoneyMin > 0 || req.CRute.MoneyMax > 0 {
			if priceFloat < float64(req.CRute.MoneyMin) || priceFloat > float64(req.CRute.MoneyMax) {
				fmt.Println("价格不符合", req.CRute.MoneyMin, req.CRute.MoneyMax, priceFloat)
				continue
			}
		}

		// 判断独享拼座舒适人数
		if item.CarPoolText == "独享" && req.CRute.Oneself && (req.CRute.OneselfMin > 0 && req.CRute.OneselfMax > 0) {
			// 独享
			if peopleNum < req.CRute.OneselfMin || peopleNum > req.CRute.OneselfMax {
				fmt.Println("独享人数不符合", req.CRute.OneselfMin, req.CRute.OneselfMax, item.CarPoolText)
				continue
			}
		} else if item.CarPoolText == "拼座" && req.CRute.Pinzuo && (req.CRute.PinzuoMin > 0 && req.CRute.PinzuoMax > 0) {
			if peopleNum < req.CRute.PinzuoMin || peopleNum > req.CRute.PinzuoMax {
				fmt.Println("拼座人数不符合", req.CRute.PinzuoMin, req.CRute.PinzuoMax, item.CarPoolText)
				continue
			}
		} else if item.CarPoolText == "舒适拼" && req.CRute.Shushi && (req.CRute.ShushiMin > 0 && req.CRute.ShushiMax > 0) {
			if peopleNum < req.CRute.ShushiMin || peopleNum > req.CRute.ShushiMax {
				fmt.Println("舒适人数不符合", req.CRute.ShushiMin, req.CRute.ShushiMax, item.CarPoolText)
				continue
			}
		}
		journeyId := ""
		if curlInfo.ReqType == ReqType1 {
			journeyId = data.DriverJourneyId
		}
		submitReq := h.GenSubmitReq(curlInfo, item, journeyId)
		submitReq.OrderDesc = recordInfo
		submitList = append(submitList, submitReq)
	}
	return submitList
}

// GenSubmitReq 构造抢单参数
func (h *GradHandler) GenSubmitReq(curlInfo *CurlInfo, item *dao.HaluoRspItem, journeyId string) *dao.SubmitReq {
	temp := strings.Split(curlInfo.GToken, TKSep)
	if len(temp) != 2 {
		return nil
	}
	tempFromDist := utils.ToFloat32(item.StartDistance)
	tempFromDist /= 1000.00

	productCode := 0
	if len(item.SimuCallPriceInfo) > 0 {
		productCode = item.SimuCallPriceInfo[0].ProductCode
		for _, simu := range item.SimuCallPriceInfo {
			if simu.CarPoolText == "独享" {
				productCode = simu.ProductCode
			}
		}
	}
	return &dao.SubmitReq{
		UserInfo:          h.Req.UserInfo,
		Phone:             h.Req.Phone,
		ApiType:           curlInfo.ApiType,
		ReqType:           curlInfo.ReqType,
		Ticket:            temp[0],
		Token:             temp[1],
		Sid:               h.Req.SidMap[temp[0]],
		SecretKey:         h.Req.SecretKey,
		Id:                item.Id,
		ArriveTime:        item.StartPlanStartTime / 1000,
		ProductCode:       productCode,    // 存在独享和拼车时，直接选独享
		StartAddr:         item.StartAddr, // 订单起点地址
		EndAddr:           item.EndAddr,   // 订单终点地址
		DriverJourneyGuid: journeyId,      // 行程路线ID
		OrderDesc:         "",             // 订单简介
		OrderFromName:     item.StartAddr.ShortAddr,
		OrderToName:       item.EndAddr.ShortAddr,
		OrderFromDist:     fmt.Sprint(tempFromDist) + "km",
	}
}

// submit 发起抢单
func (h *GradHandler) submit(req *dao.SubmitReq, curlInfo *CurlInfo) (bool, []string) {
	if h.Ws.IsStop() {
		return false, []string{}
	}
	if req.ApiType == ApiTypeApp {
		return h.submitApp(req, curlInfo)
	}
	// 使用小程序抢单，且是大厅单(没有行程路线ID)，需要发布一个行程
	h.publishJourneyAndReceive(req, curlInfo)
	return h.submitWx(req, curlInfo)
}

// submitApp APP抢单
func (h *GradHandler) submitApp(req *dao.SubmitReq, curlInfo *CurlInfo) (bool, []string) {
	/*
		订单信息：拼座1人  71元  7km 后天 21:20-21:35
		起点位置：海航互联网金融大厦-西门
		终点位置: 海口东站
		当前距离：15.3km
	*/
	recordList := make([]string, 0, 6)
	recordList = append(recordList, "抢单时间: "+utils.PrintTime(time.Time{}))
	recordList = append(recordList, "订单信息: "+req.OrderDesc)
	recordList = append(recordList, "起点位置: "+req.OrderFromName)
	recordList = append(recordList, "终点位置: "+req.OrderToName)
	//recordList = append(recordList, "当前距离: "+req.OrderFromDist)
	recordList = append(recordList, "抢单距离: 0.1km")

	bodyStr, _, err := CurlSubmit(req, curlInfo)
	if err != nil {
		recordList = append(recordList, "抢单状态: 网络错误")
		return false, recordList
	}
	var bodyObj dao.HaluoReceiveRsp
	err = json.Unmarshal([]byte(bodyStr), &bodyObj)
	if err != nil {
		fmt.Println("json解析抢单返回数据错误", err)
		recordList = append(recordList, "抢单状态: 网络错误")
		return false, recordList
	}

	if bodyObj.Code == 0 {
		// 加入PK
		joinPkSuccess, ok := bodyObj.Data["joinPkSuccess"].(bool)
		if joinPkSuccess && ok {
			h.publishJourneyAndReceive(req, curlInfo) // 加入PK之后，创建一个行程，并在此抢单
			_, _, _ = CurlSubmit(req, curlInfo)
			pkRecord := append([]string{}, recordList...)
			recordList = append(recordList, "抢单状态: 已加入PK")
			h.record(recordList)
			h.checkPk(req, pkRecord)
			return false, recordList
		}

		// 抢单成功 orderStatus":20
		orderStatus, _ := bodyObj.Data["orderStatus"].(float64)
		if int(orderStatus) == 20 {
			recordList = append(recordList, "抢单状态: 成功")
			h.errs(recordList)
			h.record(recordList)
			return true, nil
		}
		if strings.Contains(bodyStr, `"receiveOrderConflict": true`) {
			recordList = append(recordList, "抢单状态: 成功")
			h.record(recordList)
			h.errs(recordList)
			return true, nil
		}
	}
	CancelJourneyWxCurl(req) // 抢单失败，删除行程
	if bodyObj.Code == 10000 && strings.Contains(bodyObj.Msg, "faceAuthHeadPortraitImg") {
		// 需要面部识别
		msg := fmt.Sprintf("请刷入人脸(%s)", "App")
		h.StopErr(msg)
		h.warn(recordList)
		return false, nil
	}
	msg := bodyStr
	//if len(bodyObj.Msg) > 0 {
	//	msg = bodyObj.Msg
	//}
	recordList = append(recordList, "抢单状态: "+msg)
	return false, recordList
}

// submitWx 微信抢单
func (h *GradHandler) submitWx(req *dao.SubmitReq, curlInfo *CurlInfo) (bool, []string) {
	recordList := make([]string, 0, 6)
	recordList = append(recordList, "抢单时间: "+utils.PrintTime(time.Time{}))
	recordList = append(recordList, "订单信息: "+req.OrderDesc)
	recordList = append(recordList, "起点位置: "+req.OrderFromName)
	recordList = append(recordList, "终点位置: "+req.OrderToName)
	//recordList = append(recordList, "当前距离: "+req.OrderFromDist)
	recordList = append(recordList, "抢单距离: 0.1km")

	bodyStr, _, err := CurlSubmit(req, curlInfo)
	if err != nil {
		recordList = append(recordList, "抢单状态: 网络错误")
		return false, recordList
	}
	fmt.Println("抢单结果Wx", bodyStr)
	var bodyObj dao.HaluoReceiveRsp
	err = json.Unmarshal([]byte(bodyStr), &bodyObj)

	if err == nil && bodyObj.Code == 0 {
		// 抢单成功
		recordList = append(recordList, "抢单状态: 成功")
		h.record(recordList)
		return true, recordList
	}
	if bodyObj.Code == 865 {
		_, _, _ = CurlSubmit(req, curlInfo)
		// 抢单PK
		pkRecord := append([]string{}, recordList...)
		recordList = append(recordList, fmt.Sprintf("抢单状态: 正在进行PK，请等待%s秒", utils.ExtractNumber(bodyObj.Msg)))
		h.record(recordList)
		h.checkPk(req, pkRecord)
		return false, recordList
	}
	CancelJourneyWxCurl(req)
	if bodyObj.Code == 10000 {
		// 需要面部识别
		msg := fmt.Sprintf("请刷入人脸(%s)", "小程序")
		h.StopErr(msg)
		h.warn(recordList)
		return false, nil
	}

	msg := bodyStr
	if len(bodyObj.Msg) > 0 {
		msg = bodyObj.Msg
	}
	recordList = append(recordList, "抢单状态: "+msg)
	return false, recordList
}

// publishJourneyAndReceive 创建行程和自动抢单
func (h *GradHandler) publishJourneyAndReceive(req *dao.SubmitReq, curlInfo *CurlInfo) {
	if len(req.DriverJourneyGuid) <= 0 {
		// 为了更好的抢单PK，创建一个行程
		tk := req.Ticket + TKSep + req.Token
		startTime := time.Now()
		if startTime.Unix() < req.ArriveTime {
			startTime = time.Unix(req.ArriveTime, 0)
		}
		driverJourneyGuid, err := PublishJourneyWxCurl(curlInfo.Client, tk, startTime, req.StartAddr, req.EndAddr)
		req.DriverJourneyGuid = driverJourneyGuid
		if err != nil {
			fmt.Println("创建行程单错误", err)
		} else {
			// APP接口，创建行程之后，开启自动抢单
			if req.ApiType == ApiTypeApp {
				var bodyStr string
				// 如果是APP抢单，则需要创建开启自动抢单
				bodyStr, err = JourneyAutoReceive(curlInfo.Client, driverJourneyGuid, startTime, req)
				if err != nil {
					fmt.Println("行程开启自动抢单失败", err, bodyStr)
				} else {
					if !strings.Contains(bodyStr, `"functionName":"FaceVerify"`) {
						var rsp dao.HaluoRspBase
						err = json.Unmarshal([]byte(bodyStr), &rsp)
						if rsp.Code == 0 {
							fmt.Println("行程单自动接单已开启", bodyStr)
						} else {
							fmt.Println("行程单自动接单失败", bodyStr)
						}
					} else {
						// 面部识别
						//h.StopErr(fmt.Sprintf("请刷入人脸1(%s)", "App"))
						//CancelJourneyWxCurl(req)
						//return false, []string{}
					}
				}
			}
		}
	}
}

// checkPk 检查PK结果
func (h *GradHandler) checkPk(req *dao.SubmitReq, recordList []string) {
	go func() {
		startTime := time.Now()
		// 检查PK是否结束
		h.NoticePk("已加入PK")
		timeout := time.After(180 * time.Second)
		client := utils.NewHTTPClient(utils.GetProxyIpHttps())
		checkInterval := time.NewTicker(2 * time.Second)
		for {
			select {
			case <-timeout:
				// 已经超过PK时间，PK失败
				checkInterval.Stop()
				//recordList = append(recordList, "抢单状态: PK失败")
				//h.warn(recordList)
				CancelJourneyWxCurl(req) // 抢单失败，删除行程
				fmt.Println("PK失败(超时)", req.Id)
				return
			case <-h.Ws.CheckStop():
				checkInterval.Stop()
				CancelJourneyWxCurl(req) // 抢单失败，删除行程
				return
			case <-checkInterval.C:
				i := h.checkIdInCheck(req, client)
				switch i {
				case 0:
					break
				case 1:
					msg := "抢单状态: PK成功"
					if time.Now().Unix()-startTime.Unix() <= 7 {
						msg = "抢单状态: 已跳过PK，抢单成功"
					}
					recordList = append(recordList, msg)
					h.errs(recordList)
					h.record(recordList)
					h.StopSuccess()
					fmt.Println(msg, req.Id, req.OrderDesc)
					checkInterval.Stop()
					return
				case -1:
					//recordList = append(recordList, "抢单状态: PK失败")
					//h.warn(recordList)
					fmt.Println("PK失败", req.Id, req.OrderDesc)
					CancelJourneyWxCurl(req) // 抢单失败，删除行程
					checkInterval.Stop()
					return
				}
			}
		}
	}()
}

// checkIdInCheck 检查订单是否在行程中
func (h *GradHandler) checkIdInCheck(req *dao.SubmitReq, client *utils.HTTPClient) int {
	// 先查询订单状态，判断订单已完成
	detailRspStr := GetDetailV2App(client, req)
	fmt.Println("订单状态", detailRspStr)
	var detailObj dao.HaluoRspList
	err := json.Unmarshal([]byte(detailRspStr), &detailObj)
	if err != nil {
		return 0
	}
	// 判断订单是否完成
	if detailObj.Code != 100000 {
		return 0
	}

	time.Sleep(1 * time.Second)
	// 判断订单定否在已接订单中
	listRspStr := GetCheckList(req.Ticket, req.Token, client)
	fmt.Println("已抢订单", listRspStr)
	if len(listRspStr) <= 0 {
		return 0
	}
	var listRspObj dao.HaluoCheckListRsp
	err = json.Unmarshal([]byte(listRspStr), &listRspObj)
	if err != nil {
		return 0
	}
	if listRspObj.Code != 0 {
		return 0
	}
	for _, item := range listRspObj.Data {
		for _, orderItem := range item.PaxOrderList {
			if orderItem.PassengerJourneyGuid == req.Id {
				return 1
			}
		}
	}
	return -1
}

// Captcha 滑块验证码
func (h *GradHandler) Captcha(info *CurlInfo, rspStr string) bool {
	temp := strings.Split(info.CurToken, TKSep)
	if len(temp) != 2 {
		return false
	}
	ticket := temp[0]
	token := temp[1]

	sid := h.GetSid(ticket)

	var rspErr *dao.HaluoRspErr
	_ = json.Unmarshal([]byte(rspStr), &rspErr)
	reqId := rspErr.Data
	capRsp, err := GetCapId(info.Client, ticket, token, reqId, sid, info.SecretKey)
	if err != nil {
		fmt.Println("请求验证码信息错误", err)
		return false
	}
	if capRsp.Code != 0 || capRsp.Data.Success != 1 {
		return false
	}
	capId := capRsp.Data.CaptchaId

	rspCapR, err := GetJySlide(info.Client, capId)
	if err != nil {
		fmt.Println(err, "过滑块验证失败")
		return false
	}
	if rspCapR.Status != "success" {
		fmt.Println(err, "过滑块验证失败")
		return false
	}

	rspCheck, err := CheckCaptcha(info.Client, ticket, token, reqId, sid, info.SecretKey, rspCapR, capRsp)
	if err != nil {
		return false
	}
	if rspCheck.Code == 0 && rspCheck.Data.Success == 1 {
		return true
	}
	return true
}

// GetDriverJourneyIdForFTK 从行程订单中获取非抢单TK的行程ID
func (h *GradHandler) GetDriverJourneyIdForFTK(info []*CurlInfo) map[string][]string {
	list := make(map[string][]string)
	for _, curlInfo := range info {
		if curlInfo.CurToken == curlInfo.GToken || curlInfo.ReqType != ReqType1 {
			continue
		}
		if len(list[curlInfo.CurToken]) == 0 {
			list[curlInfo.CurToken] = make([]string, 0)
		}

		id, _ := curlInfo.Body["driverJourneyId"].(string)
		list[curlInfo.CurToken] = append(list[curlInfo.CurToken], id)
	}
	return list
}

// CancelJourneyForFTK 删除手动创建的刷新TK的行程
func (h *GradHandler) CancelJourneyForFTK(m map[string][]string) {
	go func() {
		msg, ok := <-h.Ws.StopChan
		fmt.Println(msg, ok)
		fmt.Println("任务结束删除刷新TK的行程", m)
		for token, list := range m {
			for _, id := range list {
				tk := strings.Split(token, TKSep)
				client := utils.NewHTTPClient(utils.GetProxyIpHttps())
				urlPath := "https://taxiapi.hellobike.com/api?hitch.driver.cancelOrder"
				bodyByte, _ := json.Marshal(GetCancelJourneyBody(tk[0], tk[1], id))
				header := GetCancelJourneyHeader()
				_, _, _ = client.Post(urlPath, bodyByte, header)
			}
		}
	}()
}

func (h *GradHandler) StopErr(msg string) {
	//发送失败信息
	data := dao.WsData{
		Type: dao.WsDataTypeErr,
		Body: msg,
	}
	dataByte, _ := json.Marshal(data)
	h.Ws.Send(dataByte)
	h.Ws.Stop()
}

func (h *GradHandler) StopSuccess() {
	// 发送成功信息
	data := dao.WsData{
		Type: dao.WsDataTypeSuccess,
		Body: "抢单成功",
	}
	dataByte, _ := json.Marshal(data)
	h.Ws.Send(dataByte)
	h.Ws.Stop()
}

func (h *GradHandler) NoticeSuccess(msg string) {
	// 通知客户端提示成功
	data := dao.WsData{
		Type: dao.WsDataTypeNoticeSuccess,
		Body: msg,
	}
	dataByte, _ := json.Marshal(data)
	h.Ws.Send(dataByte)
}

func (h *GradHandler) NoticePk(msg string) {
	// 通知客户端提示成功
	data := dao.WsData{
		Type: dao.WsDataTypeNoticePK,
		Body: msg,
	}
	dataByte, _ := json.Marshal(data)
	h.Ws.Send(dataByte)
}

func (h *GradHandler) NoticeErr(msg string) {
	// 通知客户端提示失败
	data := dao.WsData{
		Type: dao.WsDataTypeNoticeErr,
		Body: msg,
	}
	dataByte, _ := json.Marshal(data)
	h.Ws.Send(dataByte)
}

func (h *GradHandler) log(msg []string) {
	if len(msg) <= 0 {
		return
	}
	data := make([]dao.WsBodyConsoleData, 0, len(msg))
	for _, v := range msg {
		data = append(data, dao.WsBodyConsoleData{
			Type: dao.WsConsoleTypeLog,
			Msg:  v,
		})
	}
	dataByte, _ := json.Marshal(dao.BuildWsConsole(data))
	h.Ws.Send(dataByte)
}

func (h *GradHandler) info(msg []string) {
	if len(msg) <= 0 {
		return
	}
	data := make([]dao.WsBodyConsoleData, 0, len(msg))
	for _, v := range msg {
		data = append(data, dao.WsBodyConsoleData{
			Type: dao.WsConsoleTypeInfo,
			Msg:  v,
		})
	}

	dataByte, _ := json.Marshal(dao.BuildWsConsole(data))
	h.Ws.Send(dataByte)
}

func (h *GradHandler) warn(msg []string) {
	if len(msg) <= 0 {
		return
	}
	data := make([]dao.WsBodyConsoleData, 0, len(msg))
	for _, v := range msg {
		data = append(data, dao.WsBodyConsoleData{
			Type: dao.WsConsoleTypeWarn,
			Msg:  v,
		})
	}
	dataByte, _ := json.Marshal(dao.BuildWsConsole(data))
	h.Ws.Send(dataByte)
}

func (h *GradHandler) err(msg string) {
	data := make([]dao.WsBodyConsoleData, 0, len(msg))
	data = append(data, dao.WsBodyConsoleData{
		Type: dao.WsConsoleTypeErr,
		Msg:  msg,
	})
	dataByte, _ := json.Marshal(dao.BuildWsConsole(data))
	h.Ws.Send(dataByte)
}

func (h *GradHandler) errs(msg []string) {
	if len(msg) <= 0 {
		return
	}
	data := make([]dao.WsBodyConsoleData, 0, len(msg))
	for _, v := range msg {
		data = append(data, dao.WsBodyConsoleData{
			Type: dao.WsConsoleTypeErr,
			Msg:  v,
		})
	}
	dataByte, _ := json.Marshal(dao.BuildWsConsole(data))
	h.Ws.Send(dataByte)
}

func (h *GradHandler) record(msg []string) {
	data := dao.BuildWsRecord(msg)
	dataByte, _ := json.Marshal(data)
	h.Ws.Send(dataByte)
}
