var utils = {}

utils.initTime = function () {
    var currentDate = new Date();
    GYear = currentDate.getFullYear();
    GMonth = currentDate.getMonth() + 1;
    GDay = currentDate.getDate();
    var hours = currentDate.getHours();
    var minutes = currentDate.getMinutes();
    var seconds = currentDate.getSeconds();
    console.info(GYear + "-" + <PERSON>onth + "-" + GDay + " " + hours + ":" + minutes + ":" + seconds);
}

utils.GetRandStr = function(length) {
    var characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    var result = "";
    var i = 0;
    while (i < length) {
        let randomIndex = Math.floor(Math.random() * characters.length);
        result = result + characters[randomIndex];
        i++;
    }
    return result;
}

utils.GetRandChar = function(length) {
    var characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    var result = "";
    var i = 0;
    while (i < length) {
        let randomIndex = Math.floor(Math.random() * characters.length);
        result = result + characters[randomIndex];
        i++;
    }
    return result;
}

utils.GetRandStrMin = function(length) {
    var characters = "abcdefghijklmnopqrstuvwxyz0123456789";
    var result = "";
    var i = 0;
    while (i < length) {
        let randomIndex = Math.floor(Math.random() * characters.length);
        result = result + characters[randomIndex];
        i++;
    }
    return result;
}



utils.CreateGuid = function() {
    return UUIDjs.create(4);
}

utils.GetHeaderField = function(key, header) {
    return "";
}

utils.MobileEncrypte = function(mobile) {
    return RSA_Public_Encrypt(mobile);
}

utils.GetUrlParam = function(params) {
    return Object.keys(params).map(key => {
        return encodeURIComponent(key) + '=' + encodeURIComponent(params[key]);
    }).join('&');
}

utils.parseInt = function (i) {
    let t = parseInt(i);
    if (isNaN(t)) {
        return 0;
    }
    return t
}

utils.parseFloat = function(i) {
    let t = parseFloat(i);
    if (isNaN(t)) {
        return 0;
    }
    return t
}

utils.toast = function (msg) {
    console.log(msg);
    toast(msg);
}

utils.toastLog = function (msg) {
    console.log(msg);
    toast(msg);
}

utils.toastInfo = function (msg) {
    console.info(msg);
    toast(msg);
}
utils.toastErr = function (msg) {
    console.error(msg);
    toast(msg);
}


utils.compareVersion = function(version1, version2) {
    let v1 = version1.split('.');
    let v2 = version2.split('.');
    for (let i = 0; i < Math.max(v1.length, v2.length); i++) {
        let num1 = parseInt(v1[i] || 0);
        let num2 = parseInt(v2[i] || 0);
        if (num1 < num2) {
            return -1;
        } else if (num1 > num2) {
            return 1;
        }
    }

    return 0;
}

utils.GetObjLen = function(obj) {
    let count = 0;
    for (let key in obj) {
        count ++;
    }
    return count;
}


utils.IsWifiProxy = function(context) {
  importClass(android.os.Build);
  importClass(android.text.TextUtils);
  IS_ICS_OR_LATER = Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH;
  let proxyAddress;
  let proxyPort;
  if (IS_ICS_OR_LATER) {
    proxyAddress = java.lang.System.getProperty("http.proxyHost");
    portStr = java.lang.System.getProperty("http.proxyPort");
    proxyPort = java.lang.Integer.parseInt(portStr != null ? portStr : "-1");
  } else {
    proxyAddress = android.net.Proxy.getHost(context);
    proxyPort = android.net.Proxy.getPort(context);
  }
  return !TextUtils.isEmpty(proxyAddress) && proxyPort != -1;
}

utils.IsVPN = function() {
    try {
      var niList = java.net.NetworkInterface.getNetworkInterfaces();
      if (niList) {
        var VPN;
        var arry = java.util.Collections.list(niList).toArray();
        for (ary = 0; ary < arry.length; ary++) {
          if (!arry[ary].isUp() || arry[ary].getInterfaceAddresses().size() == 0) {
            continue;
          }
          if (arry[ary].getName().search("tun0") !== -1 || arry[ary].getName().search("ppp0") !== -1) {
            VPN = true;
            return true;
          } else {
            VPN = false;
          }
        }
        return VPN;
      }
    } catch (error) {}
}

utils.shuffleArray = function(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

module.exports = utils;