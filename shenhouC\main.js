"ui";
var utils = require("utils.js");
var FX = require("fx.js");
var PJYSDK = require("PJYSDK.js");
http.__okhttp__.setMaxRetries(0);
http.__okhttp__.setTimeout(5*1000);
importPackage(Packages["okhttp3"]); //导入包

var dw = device.width / 2;
var dww = device.width / 3;
ui.statusBarColor("#003300");
importClass(android.view.View);
importClass(android.graphics.Color);
importClass(android.graphics.drawable.GradientDrawable);

var IsTest = 1;
var GVERSION = "1.1.0";
var GVERSIONCode = 110;
var GAppName = "数码宝贝";
var GToolBarTitle= GAppName + "v" + GVERSION;
var db = storages.create("shenhou"); 
var GCOLOR = "#4179e7"
var BtnTextColor = "#000000";

//打开电池优化申请 判断是否加入白名单
importClass(android.os.PowerManager);
// importClass(android.Settings) //安卓setting 中有设置界面的各种activity 
var pm =  context.getSystemService(context.POWER_SERVICE);
if (!pm.isIgnoringBatteryOptimizations("com.twelve.shenhou")) {
    importClass(android.content.Intent);
    importClass(android.net.Uri)
    var intent = new Intent();
    intent.setAction("android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS");
    intent.setData(Uri.parse("package:"+"com.twelve.shenhou"))
    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
    try {
        context.startActivity(intent);
        toast("请开启忽略电池优化");
    } catch (e) {
        loge(e)
    }
}

//记录按键被按下时的触摸坐标
var x = 0, y = 0;
//记录按键被按下时的悬浮窗位置
var windowX, windowY;
//记录按键被按下的时间以便判断长按等动作
var downTime;
ui.layout(
    <vertical>
        <appbar bg="{{GCOLOR}}">
            <toolbar id="toolbar" title="{{GToolBarTitle}}" >
                <checkbox id="JmShouConsole" margin="0 -2 0 0" layout_gravity="right" marginRight="20dp"/>
                <text textSize="14dp" text="日志窗" textColor="#ffffff" textStyle="bold" layout_gravity="right"/>
            </toolbar>
            <tabs id="tabs"/>
        </appbar>
         
        <viewpager id="viewpager" bg="file://./img/bg.png">
            <ScrollView>
                <vertical id="tab1" w="*" h="*" padding="10 10 10 20">
                    <card cardElevation="2dp" cardCornerRadius="10dp" margin="5" alpha="0.9">
                        <vertical id="vdriver" marginTop="2dp"  padding="2 5 2 5">
                            <text textSize="16dp" textColor="#FF7F50" text="行程单配置" gravity="center" />
                            <horizontal margin="5 2 5 2">
                                <text textSize="16dp" textColor="black" text="行程路线："/>
                                <checkbox id="JmDRoute1" text="1" marginLeft="2dp"/>
                                <checkbox id="JmDRoute2" text="2" marginLeft="6dp"/>
                                <checkbox id="JmDRoute3" text="3" marginLeft="6dp"/>
                                <checkbox id="JmDRoute4" text="4" marginLeft="6dp"/>
                                <checkbox id="JmDRoute5" text="5" marginLeft="6dp"/>
                            </horizontal>
                            <horizontal margin="5 5 5 5">
                                <text textSize="16dp" text="订单排序："  textColor="black" h="25dp" />
                                <spinner id="JmDOrderBy" h="33dp" textSize="15dp" entries="智能排序|时间最早|离我最近|价格最高|顺路度优先" entryTextColor="{{GCOLOR}}"/>
                                <text textSize="16dp" text="  顺路 ≥ ："  textColor="black" />
                                <input inputType="number" w="50dp" id="JmShunLu" text="80" padding="5"/>
                                <text textSize="16dp" text=" %"/>
                            </horizontal>
                            <horizontal margin="5 5 5 5">
                                <text textSize="16dp" text="起点 ≤：" textColor="black"/>
                                <input inputType="number|numberDecimal" w="50dp" id="JmDFromDist" textSize="16dp" padding="5"  bg="#DDDDDD"/>
                                <text textSize="16dp" text=" km" />
                                <text textSize="16dp" text="  " />
                                <text textSize="16dp" text="        终点 ≤：" textColor="black"/>
                                <input inputType="number|numberDecimal" w="50dp" id="JmDToDist" textSize="16dp" padding="5"  bg="#DDDDDD"/>
                                <text textSize="16dp" text=" km" />
                            </horizontal>
                            <horizontal margin="5 5 5 0"  visibility="gone">
                                <text textSize="16dp" text="刷新速度：" textColor="black"/>
                                <input inputType="number" w="92dp" id="JmDFreshMin" textSize="16dp" text="1000" padding="5"  bg="#DDDDDD"/>
                                <text textSize="16dp" text=" - "/>
                                <input inputType="number" w="92dp" id="JmDFreshMax" textSize="16dp"text="2000"  padding="5"/>
                                <text textSize="16dp" text=" ms"/>
                            </horizontal>
                        </vertical>
                    </card>
                    <card cardElevation="2dp" cardCornerRadius="10dp" margin="5" alpha="0.9">
                        <vertical id="vbus" padding="2 5 2 5" >
                            <text textSize="16dp" textColor="#FF7F50" text="大厅单配置" gravity="center"/>
                            <horizontal id="hetherh3" margin="5 2 5 0" gravity="center">
                                <checkbox id="JmTab2" textSize="16dp" text="市内" marginLeft="10dp"/>
                                <checkbox id="JmTab3" textSize="16dp" text="城际" marginLeft="10dp"/>
                            </horizontal>
                            <horizontal margin="5 2 5 0">
                                <text textSize="16dp" text="订单排序：" textColor="black"/>
                                <spinner id="JmBOrderBy" h="33dp" entries="智能排序|时间最早|离我最近|价格最高" entryTextColor="{{GCOLOR}}"/>
                            </horizontal>
                            <horizontal margin="5 2 5 0">
                                <text textSize="16dp" text="起点小于：" textColor="black"/>
                                <input inputType="number|numberDecimal" w="95dp" id="JmBFromDist" textSize="16dp" padding="5"  text="30"/>
                                <text textSize="16dp" text=" km"/>
                            </horizontal>
                            <horizontal margin="5 2 5 0" >
                                <text textSize="16dp" text="订单里程：" textColor="black"/>
                                <input id="JmBOrderDist" w="95dp"   inputType="number|numberDecimal" textSize="16dp"  padding="5" text="9999"/>
                                <text textSize="16dp" text=" km"/>
                            </horizontal>
                            <horizontal margin="5 2 5 0" >
                                <text textSize="16dp" text="只接城市：" hint="使用#隔开" textColor="black"/>
                                <input id="JmNeedCity" textSize="15dp"  padding="5"  w="*" hint="使用#隔开(针对跨城)"/>
                            </horizontal>
                            <horizontal margin="5 2 5 0" >
                                <text textSize="16dp" text="不接城市：" textColor="black"/>
                                <input  id="JmNotCity" textSize="15dp"  padding="5" w="*" hint="使用#隔开(针对跨城)"/>
                            </horizontal>
                            <horizontal margin="5 2 5 0">
                                <text textSize="16dp" text="刷新速度：" textColor="black"/>
                                <input inputType="number" w="95dp" id="JmBFreshMin" textSize="16dp" text="1000" padding="5" />
                                <text textSize="16dp" text=" - "/>
                                <input inputType="number" w="95dp"id="JmBFreshMax" textSize="16dp" text="2000"  padding="5"/>
                                <text textSize="16dp" text=" ms"/>
                            </horizontal>
                        </vertical>
                    </card>
                    <card cardElevation="2dp" cardCornerRadius="10dp" margin="5" alpha="0.9">
                        <vertical id="vconf" padding="2 5 2 5">
                            <text textSize="16dp" textColor="#FF7F50" text="通用配置" gravity="center"/>
                            <horizontal margin="5 1 5 0">
                                <text textSize="16dp" text="接单接口：" textColor="black"/>
                                <radiogroup id="JmApiType"  orientation="horizontal" marginTop="-4">
                                    <radio id="JmApiTypeApp" text="APP" checked={db.get("JmApiType") == "0"} />
                                    <radio id="JmApiTypeWx" text="小程序" checked={db.get("JmApiType") == "1"} />
                                </radiogroup>
                            </horizontal>
                            <horizontal margin="5 1 5 0">
                                <text textSize="16dp" text="防       封：" textColor="black"/>
                                {/* 每刷 x 秒停 x秒 */}
                                <text textSize="16dp" text="每刷新 " textColor="black" margin="2 0 0 0"/>
                                <input textSize="16dp" inputType="number" w="50dp" id="JmFInterval" text="0" padding="5"  />
                                <text textSize="16dp" text="秒  暂停" textColor="black" margin="2 0 0 0"/>
                                <input textSize="16dp" inputType="number" w="50dp" id="JmFIntervalStop" text="0" padding="5" />
                                <text textSize="16dp" text="秒" textColor="black" margin="2 0 0 0"/>
                            </horizontal>
                            <horizontal margin="5 2 5 0">
                                <text textSize="16dp" text="订单类型：" textColor="black"/>
                                {/* 0: 独享 1: 拼车 2: 舒适 4: 送货 */}
                                <spinner id="JmOrderType" h="33dp" entries="不限|独享|拼车|舒适|送货" entryTextColor="{{GCOLOR}}"/>
                                <text textSize="16dp" text="    愿摊高速费：" textColor="black" margin="2 0 0 0"/>
                                <checkbox id="JmHighWay" margin="0 -2 0 0"/>
                            </horizontal>
                            <horizontal margin="5 1 5 1">
                                <text textSize="16dp" text="出发日期：" textColor="black" />
                                <spinner id="JmDate" textSize="16dp" h="33dp" entries="全部|今天|明天|后天" entryTextColor="{{GCOLOR}}"/>
                            </horizontal>
                            <horizontal margin="5 0 5 1">
                                <text textSize="16dp" text="出发时间：" textColor="black" gravity="left"/>
                                <input textSize="16dp" inputType="number" w="95dp" id="JmStartTime" text="0" padding="5" />
                                <text textSize="16dp"  text=" - "/>
                                <input textSize="16dp" inputType="number" w="95dp" id="JmEndTime" text="23" padding="5" />
                                <text textSize="16dp" text=" 时" textColor="black" gravity="right"/>
                            </horizontal>
                            <horizontal margin="5 2 5 1">
                                <text textSize="16dp" text="订单金额：" textColor="black" />
                                <input textSize="16dp" inputType="number|numberDecimal" w="95dp" id="JmMoneyMin" text="" padding="5"/>
                                <text textSize="16dp" text=" - "/>
                                <input textSize="16dp" inputType="number|numberDecimal" w="95dp" id="JmMoneyMax" text="" padding="5"/>
                                <text textSize="16dp" text=" 元" textColor="black" />
                            </horizontal>
                            <horizontal margin="5 2 5 1">
                                <text textSize="16dp" text="独享人数：" textColor="black" />
                                <input textSize="16dp" inputType="number" w="95dp" id="JmOneselfMin" text="3" padding="5"/>
                                <text textSize="16dp" text=" - "/>
                                <input textSize="16dp" inputType="number" w="95dp" id="JmOneselfMax" text="4" padding="5"/>
                                <text textSize="16dp" text=" 人" textColor="black" />
                                <checkbox id="JmOneself" text="" gravity="right" margin="0 -5 0 0" checked="true" visibility="gone"/>
                            </horizontal>
                            <horizontal margin="5 2 5 1">
                                <text textSize="16dp" text="拼座人数：" textColor="black" />
                                <input textSize="16dp" inputType="number" w="95dp" id="JmPinzuoMin" text="1" padding="5"/>
                                <text textSize="16dp" text=" - "/>
                                <input textSize="16dp" inputType="number" w="95dp" id="JmPinzuoMax" text="4" padding="5"/>
                                <text textSize="16dp" text=" 人" textColor="black" />
                                <checkbox id="JmPinzuo" text="" gravity="right" margin="0 -5 0 0" checked="true" visibility="gone"/>
                            </horizontal>
                            <horizontal margin="5 2 5 1">
                                <text textSize="16dp" text="舒适人数：" textColor="black" />
                                <input textSize="16dp" inputType="number" w="95dp" id="JmShushiMin" text="1" padding="5"/>
                                <text textSize="16dp" text=" - "/>
                                <input textSize="16dp" inputType="number" w="95dp" id="JmShushiMax" text="4" padding="5"/>
                                <text textSize="16dp" text=" 人" textColor="black" />
                                <checkbox id="JmShushi" text="" gravity="right" margin="0 -5 0 0" checked="true" visibility="gone"/>
                            </horizontal>
                            <horizontal id="hetherh13"  margin="5 2 5 2">
                                <text textSize="16dp" text="模拟地址：" textColor="black" />
                                <input textSize="15dp" w="*" id="JmAddress" padding="5"  hint="不填使用当前地址"/>
                            </horizontal>
                        </vertical>
                    </card>
                    <horizontal gravity="center" margin="5" h="50dp" alpha="0.9">
                        <button text="保 存" bg="{{GCOLOR}}" textColor="{{BtnTextColor}}" w="{{dw-80}}px" textStyle="bold" h="50dp" textSize="20dp" id="JmSave"/>
                        <text text="  "/>
                        <button text="启 动" bg="{{GCOLOR}}" textColor="{{BtnTextColor}}" w="{{dw-80}}px" textStyle="bold" h="50dp" textSize="20dp" id="JmStart"/>
                    </horizontal>
                </vertical>
            </ScrollView>
            <ScrollView>
                <vertical id="tab2" w="*" h="*" padding="10 10 10 20">
                    <card cardElevation="2dp" cardCornerRadius="10dp" margin="5 10 5 5"  alpha="0.9">
                        <vertical id="vlogin" padding="5">
                            <horizontal id="hetherh11"  margin="5 5 5 0" padding="5 0 5 0">
                                <text textSize="16dp" textColor="black" text="卡 密 " />
                                <input inputType="text" w="190dp" id="JmPJYCode" text="" padding="5" />
                                <button id="JmUnbind" text="解绑" layout_gravity="right|center" textSize="14dp" textColor="{{BtnTextColor}}" h="36dp"  textStyle="bold" w="60dp" marginLeft="5" alpha="1"/>
                            </horizontal>
                            <horizontal margin="5 10 5 0" id="wwwwwwb">
                                <text textSize="16dp" textColor="black" text="手机号 " gravity="left" />
                                <input inputType="number" gravity="left" w="{{dw*0.8}}px" id="JmPhone" text="" padding="5"/>  
                                <button  id="JmSendCode"  text="发送" layout_gravity="right|center" textSize="14dp" textColor="{{BtnTextColor}}" h="36dp"  textStyle="bold" w="100dp" marginLeft="5" alpha="1"/>
                            </horizontal>
                            <horizontal  margin="5 10 5 0" id="wwwwwwa">
                                <text textSize="16dp" textColor="black" text="验证码 "/>
                                <input inputType="number" gravity="center" w="{{dw*0.8}}px" id="JmStrYZM" text="" padding="5"/>
                                <button id="JmLogin" text="登录" layout_gravity="center" textSize="14dp" textColor="{{BtnTextColor}}" h="36dp" w="100dp" textStyle="bold" marginLeft="5" alpha="1"/>
                            </horizontal>
                            <input inputType="textMultiLine" gravity="top" h="200px" text="" id="JmTToken" margin="5" />
                        </vertical>
                    </card>
                    <horizontal gravity="right" margin="5" alpha="0.9">
                        <button text="保存为接单"  textColor="{{BtnTextColor}}" w="120dp" h="45dp" textSize="14dp" textStyle="bold" id="JmAddGet" margin="0 5 0 5"/>
                        <button text="保存为刷新"  textColor="{{BtnTextColor}}" w="120dp" h="45dp" textSize="14dp" textStyle="bold" id="JmAddFresh"  margin="20 5 0 5"/>
                    </horizontal>
                    <card cardElevation="2dp" cardCornerRadius="10dp" margin="5 10 5 5"  alpha="0.9" >
                        <horizontal gravity="right" margin="5"  alpha="0.9">
                            <text text="抢单TOKEN参与刷新" textStyle="bold" textSize="16dp" textColor="black"/>
                            <checkbox id="JmGTokenFresh" margin="0 -2 20 0"/>
                        </horizontal>
                    </card>
                    <card cardElevation="2dp" cardCornerRadius="10dp" margin="5" alpha="0.9">
                        <vertical gravity="center" margin="5" >
                            <text text="接单TOKEN" gravity="center" textColor="black" />
                            <input inputType="textMultiLine" gravity="top" h="300px" text="" id="JmGToken"  hint="  接单TOKEN"/> 
                        </vertical>
                    </card>
                    <card cardElevation="2dp" cardCornerRadius="10dp" margin="5" alpha="0.9">
                        <vertical gravity="center" margin="5" >
                            <text text="刷新TOKEN" gravity="center" textColor="black"/>
                            <input inputType="textMultiLine" gravity="top" minHeight="300px" text="" id="JmFToken" hint="  刷新TOKEN" />  
                        </vertical>
                    </card>
                </vertical>
            </ScrollView>
            <frame id="tab3" w="*" h="*" padding="10 10 10 20" >
                <card w="*" h="*" margin="0 0 0 45" bg="#ffffff" alpha="0.9" cardElevation="2dp" cardCornerRadius="10dp">
                    <ScrollView id="JmRecordScroll" margin="5">
                        <text padding="4" id="JmRecord"></text>
                    </ScrollView>
                </card>
                <button id="JmClearn" h="40dp" layout_gravity="center|bottom" textColor="{{BtnTextColor}}">清   除</button>
            </frame >
        </viewpager>
    </vertical>
);
ui.viewpager.setTitles(["抢单", "登录", "记录"]);
ui.tabs.setupWithViewPager(ui.viewpager);
runtime.requestPermissions(["access_fine_location"]);

function setButton(view) {
    gradientDrawable = new GradientDrawable();
    gradientDrawable.setShape(GradientDrawable.RECTANGLE);
    gradientDrawable.setStroke(3, colors.parseColor("#4179e7"));
    gradientDrawable.setCornerRadius(20);
    gradientDrawable.setSize(50, 50);
    gradientDrawable.setColor(colors.parseColor("#4179e7"));
    view.setBackground(gradientDrawable);
}

setButton(ui.JmSave);
setButton(ui.JmStart);

setButton(ui.JmSendCode);
setButton(ui.JmLogin);
setButton(ui.JmAddGet);
setButton(ui.JmAddFresh);
setButton(ui.JmClearn);
setButton(ui.JmUnbind);

function setInput(view) {
    gradientDrawable = new GradientDrawable();
    gradientDrawable.setShape(GradientDrawable.RECTANGLE);
    gradientDrawable.setStroke(3, colors.parseColor("#000000"));//线的宽度 与 线的颜色
    gradientDrawable.setCornerRadius(20);
    gradientDrawable.setSize(50, 50);
    gradientDrawable.setColor(colors.parseColor("#F8F8F8"));
    view.setBackground(gradientDrawable);
}

setInput(ui.JmDFreshMin);
setInput(ui.JmDFreshMax);
setInput(ui.JmDFromDist);
setInput(ui.JmDToDist);
setInput(ui.JmDOrderBy);

setInput(ui.JmFInterval);
setInput(ui.JmFIntervalStop);
setInput(ui.JmBOrderBy);
setInput(ui.JmBOrderDist);
setInput(ui.JmBFromDist);
setInput(ui.JmNeedCity);
setInput(ui.JmNotCity);
setInput(ui.JmBFreshMin);
setInput(ui.JmBFreshMax);

setInput(ui.JmOrderType);
setInput(ui.JmDate);
setInput(ui.JmStartTime);
setInput(ui.JmEndTime);
setInput(ui.JmMoneyMin);
setInput(ui.JmMoneyMax);
setInput(ui.JmOneselfMin);
setInput(ui.JmOneselfMax);
setInput(ui.JmPinzuoMin);
setInput(ui.JmPinzuoMax);
setInput(ui.JmShushiMin);
setInput(ui.JmShushiMax);
setInput(ui.JmShunLu);
setInput(ui.JmAddress);

setInput(ui.JmPJYCode);
setInput(ui.JmPhone);
setInput(ui.JmStrYZM);
setInput(ui.JmTToken);
setInput(ui.JmGToken);
setInput(ui.JmFToken);

var GStop = true;
var GSaveFlag = 1; // 1初始状体 2保存成功 3保存中(防止重复点击)
var GShowConsole = 1;

var GLatL = "";
var GLngL = "";
var GLatS = "";
var GLngS = "";
var GCityName = "";
var GCityId = "";
var GAdCode = "";
var GCityCode = "";


// 行程单
var GDRoute1 = false;
var GDRoute2 = false;
var GDRoute3 = false;
var GDRoute4 = false;
var GDRoute5 = false;
var GDRoute6 = false;
var GDRoute7 = false;
var GDRoute8 = false;
var GDRoute9 = false;
var GDRouteIndex = [];
var GDRouteIds = {};

var GShunLu = 80; // 顺路度
var GDOrderBy = 0;
var GDFromDist = 0;
var GDToDist = 0;
// 行程单刷新速度
var GDFreshMin = 500;
var GDFreshMax = 1000;

// 大厅单
var GTab2 = false; // 市内
var GTab3 = false; // 城际
var GTabIds = [];
var GBOrderBy = 0;
var GOrderType = 0; // 订单类型
var GBFromDist = 0;
var GBOrderDist = 0;
var GNeedCity = ""; // 只接城市
var GNotCity = ""; // 不接城市
var GBFreshMin = 500;// 大厅单刷新速率
var GBFreshMax = 1000;

// 常用
var GApiType = 0;
var GFInterval = 0; // 防封
var GFIntervalStop = 0;
var GDate = 0; // 日期 0: 无 1:今天 2:明天 3:后天
var GHighWay = false; // 只接已支付
var GStartH = 0; // 开始时间小时
var GEndH = 23; // 结束时间小时
var GMoneyMin = 0; // 最小金额
var GMoneyMax = 0; // 最大金额
// 独享
var GOneself = false;
var GOneselfMin = 3;
var GOneselfMax = 4;
// 拼座
var GPinzuo = false;
var GPinzuoMin = 1;
var GPinzuoMax = 4;
// 舒适
var GShushi = false;
var GShushiMin = 1;
var GShushiMax = 4;
var GAddress = ""; // 地址

var GGToken = "";
var GFToken = "";
var GGTokenFresh = false;
var GPhone = "";

var GCarNum = "";

// 启动控制台
threads.start(function() {
    CheckProxy();
    console.show(); //程序结束自动 隐藏控制台
    console.setTitle(GAppName+GVERSION, "#4179e7", 33);
    console.setMaxLines(100);
    ui.JmShouConsole.checked = true;
    sleep(50);
    console.setSize(device.width * 0.8, device.height * 0.3);
    console.log("程序启动");
    checkVersion(); // 检查版本
});
function Notice() {
    threads.start(function() {
        let aa = db.get("notice");
        if (aa == getDate()) {
            return
        }
        try {
            let data = pjysdk.GetRemoteVar("notice");
            if (data.code != 0 || data.result.value == "" || data.result.value == undefined) {
                return
            }
            alert("公告", data.result.value);
            db.put("notice", getDate());
        } catch (e) {
        }
    });
}
// 检查是否使用代理
function CheckProxy() {
    if (IsTest == 1) {
        return
    }
    threads.start(function() {
        sleep(2000);
        if (utils.IsWifiProxy(context) || utils.IsVPN()) {
            exit();
        }
        setInterval(() => {
            if (utils.IsWifiProxy(context) || utils.IsVPN()) {
                exit();
            }
        }, 10000);
    });
}

// 日志窗口
ui.JmShouConsole.click(function() {
    threads.start(function() {
        if (ui.JmShouConsole.checked) {
            console.show(true);
            sleep(50);
            console.setSize(device.width*0.8, device.height * 0.3);
        } else {
            console.hide();
        }
    });
});

function isNanToEmpty(t) {
    let i = parseInt(t);
    if (isNaN(i) || i == 0) {
        return "";
    }
    return i + "";
}

function checkVersion() {
    threads.start(function() {
        try {
            let res = pjysdk.GetSoftwareLatestVersion(GVERSIONCode);
            if (res.code == 0) {
                let title = "检测到新版本";
                let msg = "";
                //{"code":0,"message":"ok","result":{"version":"1.0.1","url":"https://zbys.lanzouh.com/b058rqm7g","notice":"新版本公告","server_time":1721129507}}
                
                if (utils.compareVersion(res.result.version, GVERSION) <= 0) {
                    Notice();
                    return
                }
                
                if (res.result.notice != undefined || res.result.notice != "") {
                    msg += "\n" + res.result.notice
                }
                let clear = confirm(title, msg);
                if (clear) {
                    app.openUrl(res.result.url);
                }
            } else {
                Notice();
            }
        } catch(e) {
        }  
    });
}

// 初始化界面数据
initJmData();
function initJmData() {
    if (db.get("GAdCode") != undefined) {
        GAdCode = parseInt(db.get("GAdCode") + "");
    }
    if (db.get("GCityId") != undefined) {
        GCityId = parseInt(db.get("GCityId") + "");
    }
    if (db.get("GCityName") != undefined) {
        GCityName = parseInt(db.get("GCityName") + "");
    }

    // 行程单
    if (db.get("JmDRoute1") != undefined) {
        ui.JmDRoute1.checked = db.get("JmDRoute1") == "1";
    }
    if (db.get("JmDRoute2") != undefined) {
        ui.JmDRoute2.checked = db.get("JmDRoute2") == "1";
    }
    if (db.get("JmDRoute3") != undefined) {
        ui.JmDRoute3.checked = db.get("JmDRoute3") == "1";
    }
    if (db.get("JmDRoute4") != undefined) {
        ui.JmDRoute4.checked = db.get("JmDRoute4") == "1";
    }
    if (db.get("JmDRoute5") != undefined) {
        ui.JmDRoute5.checked = db.get("JmDRoute5") == "1";
    }
    // if (db.get("JmDRoute6") != undefined) {
    //     ui.JmDRoute6.checked = db.get("JmDRoute6") == "1";
    // }
    // if (db.get("JmDRoute7") != undefined) {
    //     ui.JmDRoute7.checked = db.get("JmDRoute7") == "1";
    // }
    // if (db.get("JmDRoute8") != undefined) {
    //     ui.JmDRoute8.checked = db.get("JmDRoute8") == "1";
    // }
    // if (db.get("JmDRoute9") != undefined) {
    //     ui.JmDRoute9.checked = db.get("JmDRoute9") == "1";
    // }
    // 顺路
    if (db.get("JmShunLu") != undefined) {
        ui.JmShunLu.setText(isNanToEmpty(db.get("JmShunLu")));
    }
    // 行程单订单排序
    if (db.get("JmDOrderBy") != undefined) {
        ui.JmDOrderBy.setSelection(isNanToEmpty(db.get("JmDOrderBy")));
    }
    // 行程单起点终点距离
    if (db.get("JmDFromDist") != undefined) {
        ui.JmDFromDist.setText(db.get("JmDFromDist"));
    }
    if (db.get("JmDToDist") != undefined) {
        ui.JmDToDist.setText(db.get("JmDToDist"));
    }
    // 行程单刷新速度
    if (db.get("JmDFreshMin") != undefined) {
        ui.JmDFreshMin.setText(db.get("JmDFreshMin"));
    }
    if (db.get("JmDFreshMax") != undefined) {
        ui.JmDFreshMax.setText(db.get("JmDFreshMax"));
    }

    // 大厅单
    if (db.get("JmTab2") != undefined) {
        ui.JmTab2.checked = db.get("JmTab2") == "1";
    }
    if (db.get("JmTab3") != undefined) {
        ui.JmTab3.checked = db.get("JmTab3") == "1";
    }
    // 大厅单订单排序
    if (db.get("JmBOrderBy") != undefined) {
        ui.JmBOrderBy.setSelection(isNanToEmpty(db.get("JmBOrderBy") ));
    }
    // 起点小于
    if (db.get("JmBFromDist") != undefined) {
        ui.JmBFromDist.setText(db.get("JmBFromDist")+"");
    }
    // 订单里程
    if (db.get("JmBOrderDist") != undefined) {
        ui.JmBOrderDist.setText(db.get("JmBOrderDist")+"");
    }
    // 跨城城市限定
    if (db.get("JmNeedCity") != undefined) {
        ui.JmNeedCity.setText(db.get("JmNeedCity") + "");
    }
    if (db.get("JmNotCity") != undefined) {
        ui.JmNotCity.setText(db.get("JmNotCity") + "");
    }
    // 大厅单刷新速度
    if (db.get("JmBFreshMin") != undefined) {
        ui.JmBFreshMin.setText(db.get("JmBFreshMin"));
    }
    if (db.get("JmBFreshMax") != undefined) {
        ui.JmBFreshMax.setText(db.get("JmBFreshMax"));
    }
    
    // 常用
    // 防封
    if (db.get("JmFInterval") != undefined) {
        ui.JmFInterval.setText(isNanToEmpty(db.get("JmFInterval")));
    }
    if (db.get("JmFIntervalStop") != undefined) {
        ui.JmFIntervalStop.setText(isNanToEmpty(db.get("JmFIntervalStop")));
    }
    // 订单类型
    if (db.get("JmOrderType") != undefined) {
        ui.JmOrderType.setSelection(isNanToEmpty(db.get("JmOrderType")));
    }
    // 出发日期
    if (db.get("JmDate") != undefined) {
        ui.JmDate.setSelection(parseInt(db.get("JmDate")));
    }
    // 源摊高速费
    if (db.get("JmHighWay") != undefined) {
        ui.JmHighWay.checked = db.get("JmHighWay") == "1";
    }
    // 出发时间
    if (db.get("JmStartTime") != undefined) {
        ui.JmStartTime.setText(isNanToEmpty(db.get("JmStartTime")));
    }
    if (db.get("JmEndTime") != undefined) {
        ui.JmEndTime.setText(isNanToEmpty(db.get("JmEndTime")));
    }
    // 金额
    if (db.get("JmMoneyMin") != undefined) {
        ui.JmMoneyMin.setText(isNanToEmpty(db.get("JmMoneyMin")));
    }
    if (db.get("JmMoneyMax") != undefined) {
        ui.JmMoneyMax.setText(isNanToEmpty(db.get("JmMoneyMax")));
    }

    // 独享
    // if (db.get("JmOneself") != undefined) {
    //     ui.JmOneself.checked = db.get("JmOneself") == "1";
    // }
    if (db.get("JmOneselfMin") != undefined) {
        ui.JmOneselfMin.setText(isNanToEmpty(db.get("JmOneselfMin")));
    }
    if (db.get("JmOneselfMax") != undefined) {
        ui.JmOneselfMax.setText(isNanToEmpty(db.get("JmOneselfMax")));
    }
    // 拼座
    // if (db.get("JmPinzuo") != undefined) {
    //     ui.JmPinzuo.checked = db.get("JmPinzuo") == "1";
    // }
    if (db.get("JmPinzuoMin") != undefined) {
        ui.JmPinzuoMin.setText(isNanToEmpty(db.get("JmPinzuoMin")));
    }
    if (db.get("JmPinzuoMax") != undefined) {
        ui.JmPinzuoMax.setText(isNanToEmpty(db.get("JmPinzuoMax")));
    }
    // 舒适
    // if (db.get("JmShushi") != undefined) {
    //     ui.JmShushi.checked = db.get("JmShushi") == "1";
    // }
    if (db.get("JmShushiMin") != undefined) {
        ui.JmShushiMin.setText(isNanToEmpty(db.get("JmShushiMin")));
    }
    if (db.get("JmShushiMax") != undefined) {
        ui.JmShushiMax.setText(isNanToEmpty(db.get("JmShushiMax")));
    }
    // 地址
    if (db.get("JmAddress") != undefined) {
        ui.JmAddress.setText(db.get("JmAddress") + "");
    }

    // 卡密
    if (db.get("JmPJYCode") != undefined) {
        ui.JmPJYCode.setText(db.get("JmPJYCode") + "");
        PJYCode = db.get("JmPJYCode") + "";
    }
    // 手机号码
    if (db.get("JmPhone") != undefined) {
        ui.JmPhone.setText(db.get("JmPhone") + "");
    }
    //登录token
    if (db.get("JmGToken") != undefined) {
        ui.JmGToken.setText(db.get("JmGToken") + "");
    }
    if (db.get("JmFToken") != undefined) {
        ui.JmFToken.setText(db.get("JmFToken") + "");
    }
    if (db.get("JmGTokenFresh") != undefined) {
        ui.JmGTokenFresh.checked = db.get("JmGTokenFresh") == "1";
    }
}

var AppKey = "csq55njdquspgbckl010";
var AppSecret = "G8WvQJyxTPa3bi3mRmJScJD832uOROqk";
var PJYPwd = "123456";
let pjysdk = new PJYSDK(AppKey, AppSecret);
pjysdk.debug = false;

var PJYCode = ""; // PJYCode
var PJYIsLogin = 0; // 卡密是否已登录

// 验证卡密是否登录了
function PJYLogin(card) {
    // 校验卡密是否正确
    if (card == "") {
        console.log("卡密不能为空");
        return false;
    }
    try {
        if (PJYIsLogin == 0) { // 未登录，需要判断登录
            pjysdk.SetCard(card); // 设置卡密
            pjyToken = db.get("PJYToken");
            if (pjyToken != undefined && pjyToken != "") {
                // console.log("正在登录...");
                pjysdk._token = pjyToken;
                pjysdk.CardLogout(pjyToken);
            }

            let login_ret = pjysdk.CardLogin();
            if (login_ret.code === 0) {
                toastLog("到期时间：" + login_ret.result.expires);
                db.put("PJYToken", login_ret.result.token);
                console.log("登录成功");
                PJYIsLogin = 1; // 标记为卡密已经登录
                // pjysdk.SetCardUnbindPassword(PJYPwd);
                return true;
            } else {
                toast(login_ret.message);
                console.log(login_ret.message)
                return false;
            }
        }
    } catch (e) {
        console.log(e);
        console.log("卡密登录失败");
        return false;
    }
    return true;
}

// 监听心跳失败事件
pjysdk.event.on("heartbeat_failed", function(hret) {
    toastLog(hret.message);
    if (hret.code === 10214) {
        sleep(200);
        exit();  // 退出脚本
        return
    }
    log("心跳失败，尝试重登...")
    sleep(2000);
    let login_ret = pjysdk.CardLogin();
    if (login_ret.code == 0) {
        log("重登成功");
    } else {
        toastLog(login_ret.message);  // 重登失败
        sleep(200);
        exit();  // 退出脚本
    }
});

// 当脚本正常或者异常退出时会触发exit事件
events.on("exit", function(){
    pjysdk.CardLogout(); // 调用退出登录
    log("结束运行");
    console.hide();
});

// 泡椒云设置车牌
function PJYSetCarNum(carNum) {
    let ret = pjysdk.UpdateCardConfig(carNum)
    // {"code":0,"message":"ok","nonce":"cqb58brdqusodj4p5j4g","sign":"a2492d28d3c7fdc1f5e27deb7278b7c5"}
    if (ret.code == 0) {
        return true;
    }
    return false;
}

// 大厅单勾选判断
ui.JmTab2.click(function() {
    if (ui.JmTab2.checked) {
        ui.JmTab3.checked = false;
    }
});
ui.JmTab3.click(function() {
    if (ui.JmTab3.checked) {
        ui.JmTab2.checked = false;
    }
});

// 清除记录
ui.JmClearn.click(function() {
    ui.run(function() {
        ui.JmRecord.setText("");
        setTimeout(() => {
            ui.JmRecordScroll.fullScroll(View.FOCUS_UP);
        }, 200);
    })
});

// 解绑
ui.JmUnbind.click(function() {
    // 获取卡密
    let card = ui.JmPJYCode.getText() + "";
    if (card == "") {
        utils.toastLog("卡密不能为空");
        return
    }

    // GGToken = ui.JmGToken.getText() + ""; // 抢单token
    // if (GGToken == undefined || GGToken == "") {
    //     utils.toastLog("抢单token不能为空");
    //     return
    // }
    threads.start(function() {
        pjysdk.SetCard(card);
        let res = pjysdk.CardUnbindDeviceByPassword(PJYPwd);
        if (res.code == 0) {
            utils.toastInfo("解绑成功");
        } else {
            utils.toastErr("解绑失败");
        }
        // // 获取车辆信息
        // cardInfo = FX.GetDriver(GGToken);
        // if (cardInfo == "") {
        //     return;
        // }
        // cardNum = cardInfo["carNum"];
        // PJYIsLogin = 0;
        // pjysdk.setDeviceParam(cardNum, "");
        // // 验证卡密是否登录
        // if (PJYLogin(card) == false) {
        //     return;
        // }
        // let pjyToken = db.get("PJYToken");
        // if (pjyToken == undefined || pjyToken == "") {
        //     utils.toastLog("为获取到卡密TOKEN");
        //     return;
        // }

        // let deviceId = pjysdk.getDeviceIDLong();
        // let ip = "dbe.012331.com";
        // ip="127.0.0.1";
        // let url = "http://"+ip+":8888/unbindShenhou?card="+ card +"&device_id="+encodeURIComponent(deviceId)+"&token=" + pjyToken;
        
        // // 解绑
        // try {
        //     let res = http.get(url);
        //     let obj = res.body.json();
        //     if (obj.errno != 0) {
        //         utils.toastLog(obj.msg);
        //         return
        //     }
        //     utils.toastInfo("解绑成功");
        //     // 登录初始化
        //     PJYIsLogin = 0;
        //     GSaveFlag = 1;
        // } catch (e) {
        //     utils.toastLog("解绑失败");
        //     return
        // }
    });
});

// 发送验证码
var SendCodeTimeOutFlag = 0;
ui.JmSendCode.click(function() {
    console.log("准备发送验证码");
    if (SendCodeTimeOutFlag == 1) {
        console.log("已发送过,请过一分钟发验证码");
        return;
    }
    ui.JmSendCode.setClickable(false);
    
    threads.start(function() {
        var phone = ui.JmPhone.getText() + "";
        db.put("JmPhone", phone);
        var res = FX.SendCode(phone)
        if (res == 1) {
            SendCodeTimeOutFlag = 1;
            utils.toastInfo("验证码已发送");
            setTimeout(function () {
                SendCodeTimeOutFlag = 0;
                ui.JmSendCode.setClickable(true);
            }, 6000)
            return
        }
        // 发送验证码
        if (res == 2) {
            // 需要发送验证码
            return;
        }
        ui.JmSendCode.setClickable(true);
    });
});

// 登录
ui.JmLogin.click(function() {
    threads.start(function() {
        let phone = ui.JmPhone.getText() + "";
        let code = ui.JmStrYZM.getText() + "";
        if (code == "") {
            utils.toastLog("请输入验证码");
            return;
        }
        var res = "";
        try {
            res = FX.Login(phone, code); 
        } catch (e) {
            console.log(e, res);
            utils.toastErr("登录出现异常");
            return;
        }
        if (res == "" || res.code != 0) {
            let msg = "登录未成功，请检查网络2";
            if (res.msg != undefined && res.msg != "") {
                msg = res.msg;
            }
            console.log(res);
            utils.toastErr(msg);
            return;
        }
        let ticket = res.data.ticket + "";
        let token = res.data.token + "";
        if (ticket == undefined || ticket == "undefined") {
            console.info(res);
            utils.toastErr("登录未成功，请检查网络3");
            return;
        }
        db.put(token, phone);
        let allToken = ticket + "&*&" + token;
        ui.run(function() {
            ui.JmTToken.setText(allToken);
        });
        db.put("JmTToken", allToken);
        db.put("JmPhone", phone);
        utils.toastInfo("登录成功");
    });
});

// 保存抢单Token
ui.JmAddGet.click(function() {
    ui.JmAddGet.setClickable(false);
    let token = ui.JmTToken.getText();
    if (token.length == 0) {
        utils.toastLog("请先获取TOKEN");
        ui.JmAddGet.setClickable(true);
        retrun;
    }
    ui.JmGToken.setText(token);
    db.put("JmGToken", token+"");
    GGToken = token+"";
    ui.JmAddGet.setClickable(true);
});
// 保存刷单Token
ui.JmAddFresh.click(function() {
    ui.JmAddFresh.setClickable(false);
    let token = ui.JmTToken.getText();
    if (token.length == 0) {
        utils.toastLog("请先获取TOKEN");
        ui.JmAddFresh.setClickable(true);
        retrun;
    }
    let oldToken = ui.JmFToken.getText() + "";
    if (oldToken != "") {
        oldToken += "\n\n";
    }
    let tokenAll = oldToken + token + "";
    ui.JmFToken.setText(tokenAll);
    db.put("JmFToken", tokenAll);
    GFToken = tokenAll;
    ui.JmAddFresh.setClickable(true);
});
ui.JmGTokenFresh.click(function() {
    db.put("JmGTokenFresh", ui.JmGTokenFresh.checked ? "1" : "0");
})

function saveing() {
    ui.run(function() {
        ui.JmSave.setClickable(false);
        ui.JmSave.setText("保存中");
    });
}

function saveOver() {
    GSaveFlag = 1;
    ui.run(function(){
        ui.JmSave.setClickable(true);
        ui.JmSave.setText("保 存");
    });
}

// 保存界面数据
ui.JmSave.click(function() {
    saveing();
    if (GSaveFlag == 3) {
        console.info("上次还未保存完！！！");
        ui.JmSave.setClickable(true);
        ui.JmSave.setText("保 存");
        return;
    }
    GSaveFlag = 3;
    console.info("正在保存");
    threads.start(function() {
        utils.initTime(); // 输出当前时间
        // 获取地址
        try {
            GAddress = ui.JmAddress.getText();
            db.put("JmAddress", GAddress+"");
            setLoaction(GAddress); // 获取经纬度保存
        } catch (e) {
            console.log(e);
            utils.toastErr("请检查网络是否连接，未取到定位，请打开哈啰再试")
            saveOver();
            return;
        }
        // 登录界面
        GGToken = ui.JmGToken.getText() + ""; // 抢单token
        GFToken = ui.JmFToken.getText() + ""; // 刷单token
        if (GGToken == "" || GGToken == "undefined" || GFToken == "" || GFToken == "undefined" ) {
            utils.toastLog("请先设置刷单和抢单Token");
            saveOver();
            return;
        }
        // 保存TOKEN
        db.put("JmGToken", GGToken);
        db.put("JmFToken", GFToken);
        GGTokenFresh = ui.JmGTokenFresh.checked; // 抢单Token也一起刷单

        GPhone = ui.JmPhone.getText() + "";
        // 获取车辆信息
        cardInfo = FX.GetDriver(GGToken);
        if (cardInfo == "") {
            saveOver();
            return;
        }
        GCarNum = cardInfo["carNum"];
        if (GCarNum == "" || GCarNum == undefined) {
            utils.toast("获取抢单车辆信息错误");
            saveOver();
            return;
        }
        pjysdk.setDeviceParam(GCarNum, "");
         // 验证卡密是否登录
        card = ui.JmPJYCode.getText() + "";
        if (card != PJYCode) {
            PJYIsLogin = 0;
        }
        PJYCode = card;
        if (PJYLogin(PJYCode) == false) {
            saveOver();
            return;
        }
        db.put("JmPJYCode", PJYCode);
        // 记录gvid
        if (!PJYSetCarNum(GCarNum)) {
            utils.toastLog("保存卡密信息错误");
            saveOver();
            return;
        }

        // 行程单
        GDRoute1 = ui.JmDRoute1.checked;
        GDRoute2 = ui.JmDRoute2.checked;
        GDRoute3 = ui.JmDRoute3.checked;
        GDRoute4 = ui.JmDRoute4.checked;
        GDRoute5 = ui.JmDRoute5.checked;
        // GDRoute6 = ui.JmDRoute6.checked;
        // GDRoute7 = ui.JmDRoute7.checked;
        // GDRoute8 = ui.JmDRoute8.checked;
        // GDRoute9 = ui.JmDRoute9.checked;

        GShunLu = utils. parseInt(ui.JmShunLu.getText()); // 顺路
        GDOrderBy = ui.JmDOrderBy.getSelectedItemPosition(); // 行程单订单排序
        // 行程单起点终点距离
        GDFromDist = utils.parseFloat(ui.JmDFromDist.getText());
        GDToDist = utils.parseFloat(ui.JmDToDist.getText());
        // 行程单刷新速度
        GDFreshMin = utils.parseInt(ui.JmDFreshMin.getText()); // 行程单最小刷新时间
        GDFreshMax = utils.parseInt(ui.JmDFreshMax.getText()); // 行程单最大刷新时间
        if (GDFreshMin > GDFreshMax) {
            utils.toastLog("刷新时间错误");
            saveOver();
            return;
        }

        // 大厅单
        GTab2 = ui.JmTab2.checked; // 选中市内单
        GTab3 = ui.JmTab3.checked; // 选中跨城单
        GBOrderBy = ui.JmBOrderBy.getSelectedItemPosition(); // 大厅单订单排序
        GBFromDist = utils.parseInt(ui.JmBFromDist.getText()); // 大厅单起点距离
        GBOrderDist = utils.parseInt(ui.JmBOrderDist.getText()); // 大厅单订单里程
        GNeedCity = ui.JmNeedCity.getText() + "";// 只接城市
        GNotCity = ui.JmNotCity.getText() + "";// 不接城市
        // 大厅单刷新速度
        GBFreshMin = utils.parseInt(ui.JmBFreshMin.getText()); // 大厅单最小刷新时间
        GBFreshMax = utils.parseInt(ui.JmBFreshMax.getText()); // 大厅单最大刷新时间
        if (GBFreshMin > GBFreshMax) {
            utils.toastLog("刷新速度错误");
            saveOver();
            return;
        }

        // 通用配置
        // GApiType = ui.JmApiType.getCheckedRadioButtonId();
        let checkedId = ui.JmApiType.getCheckedRadioButtonId();
        if (checkedId === -1) {
            utils.toastLog("请勾选接单接口");
            saveOver();
            return;
        }  else {
            // 根据id获取勾选的radio
            let checkedRadio = ui.JmApiType.findViewById(checkedId);
            // 获取勾选的位置
            GApiType = ui.JmApiType.indexOfChild(checkedRadio);
        }

        GFInterval = utils.parseInt(ui.JmFInterval.getText());// 防封
        GFIntervalStop = utils.parseInt(ui.JmFIntervalStop.getText());// 防封
        GOrderType = ui.JmOrderType.getSelectedItemPosition(); // 订单类型
        GDate = ui.JmDate.getSelectedItemPosition();// 接单日期
        GHighWay = ui.JmHighWay.checked; // 是否只接已付
        GStartH = utils.parseInt(ui.JmStartTime.getText()); // 开始时间
        GEndH = utils.parseInt(ui.JmEndTime.getText()) ; // 结束时间
        if (GStartH >= 24) {
            GStartH = 0;
        }
        if (GEndH <= 0) {
            GEndH = 24;
        }

        GMoneyMin = utils.parseInt(ui.JmMoneyMin.getText()); // 最小金额
        GMoneyMax = utils.parseInt(ui.JmMoneyMax.getText()); // 最大金额

        GOneself = ui.JmOneself.checked; // 是否独享
        GOneselfMin = utils.parseInt(ui.JmOneselfMin.getText()); // 最小独享人数
        GOneselfMax = utils.parseInt(ui.JmOneselfMax.getText()); // 最大独享人数
        GPinzuo = ui.JmPinzuo.checked; // 是否拼座
        GPinzuoMin = utils.parseInt(ui.JmPinzuoMin.getText()); // 最小拼座人数
        GPinzuoMax = utils.parseInt(ui.JmPinzuoMax.getText()); // 最大拼座人数
        GShushi = ui.JmShushi.checked; // 是否舒适
        GShushiMin = utils.parseInt(ui.JmShushiMin.getText()); // 最小舒适人数
        GShushiMax = utils.parseInt(ui.JmShushiMax.getText()); // 最大舒适人数

        // 行程单
        db.put("JmDRoute1", GDRoute1 ? "1" : "0");
        db.put("JmDRoute2", GDRoute2 ? "1" : "0");
        db.put("JmDRoute3", GDRoute3 ? "1" : "0");
        db.put("JmDRoute4", GDRoute4 ? "1" : "0");
        db.put("JmDRoute5", GDRoute5 ? "1" : "0");
        // db.put("JmDRoute6", GDRoute6 ? "1" : "0");
        // db.put("JmDRoute7", GDRoute7 ? "1" : "0");
        // db.put("JmDRoute8", GDRoute8 ? "1" : "0");
        // db.put("JmDRoute9", GDRoute9 ? "1" : "0");

        db.put("JmShunLu", GShunLu); // 顺路
        db.put("JmDOrderBy", GDOrderBy + ""); // 行程单排序
        db.put("JmDFromDist", GDFromDist + ""); //行程单起点终点距离
        db.put("JmDToDist", GDToDist + "");
        db.put("JmDFreshMin", GDFreshMin+""); // 行程单刷新速率
        db.put("JmDFreshMax", GDFreshMax+"");

        // 大厅单
        db.put("JmTab2", GTab2 ? "1" : "0");
        db.put("JmTab3", GTab3 ? "1" : "0");
        db.put("JmBOrderBy", GBOrderBy + ""); // 大厅单排序 
        db.put("JmBFromDist", GBFromDist+""); // 行程单起点小于
        db.put("JmBOrderDist", GBOrderDist+""); // 行程单订单里程
        db.put("JmNeedCity", GNeedCity+""); // 只接城市
        db.put("JmNotCity", GNotCity+""); // 不接城市
        db.put("JmBFreshMin", GBFreshMin+""); // 大厅单刷新速率
        db.put("JmBFreshMax", GBFreshMax+"");
        
        // 通用配置
        db.put("JmApiType", GApiType+""); // 接单接口
        db.put("JmFInterval", GFInterval+""); // 防封
        db.put("JmFIntervalStop", GFIntervalStop+"");
        db.put("JmOrderType", GOrderType); // 订单类型
        db.put("JmDate", GDate); // 日期
        db.put("JmHighWay", GHighWay ? "1" : "0"); // 是否只接已支付
        db.put("JmStartTime", GStartH); // 小时
        db.put("JmEndTime", GEndH);
        db.put("JmMoneyMin", GMoneyMin+""); // 金额
        db.put("JmMoneyMax", GMoneyMax+"");

        //独享
        db.put("JmOneself", GOneself ? "1" : "0");
        db.put("JmOneselfMin", GOneselfMin+"");
        db.put("JmOneselfMax", GOneselfMax+"");
        // 拼座
        db.put("JmPinzuo", GPinzuo ? "1" : "0");
        db.put("JmPinzuoMin", GPinzuoMin+"");
        db.put("JmPinzuoMax", GPinzuoMax+"");
        // 舒适
        db.put("JmShushi", GShushi ? "1" : "0");
        db.put("JmShushiMin", GShushiMin+"");
        db.put("JmShushiMax", GShushiMax+"");

        GDRouteIndex = [];
        if (GDRoute1) {
            GDRouteIndex.push(1);
        }
        if (GDRoute2) {
            GDRouteIndex.push(2);
        }
        if (GDRoute3) {
            GDRouteIndex.push(3);
        }
        if (GDRoute4) {
            GDRouteIndex.push(4);
        }
        if (GDRoute5) {
            GDRouteIndex.push(5);
        }
        // if (GDRoute6) {
        //     GDRouteIndex.push(6);
        // }
        // if (GDRoute7) {
        //     GDRouteIndex.push(7);
        // }
        // if (GDRoute8) {
        //     GDRouteIndex.push(8);
        // }
        // if (GDRoute9) {
        //     GDRouteIndex.push(9);
        // }

        GTabIds = [];
        if (GTab2) {
            GTabIds.push(2);
        }
        if (GTab3) {
            GTabIds.push(3);
        }
        // 行程单, 获取司机行程路线
        GDRouteIds = {}; 
        if (GDRouteIndex.length > 0) {
            GDRouteIds = FX.GetDRouteId(GDRouteIndex, GGToken, GLatL, GLngL);
            if (utils.GetObjLen(GDRouteIds) != GDRouteIndex.length) {
                console.log("获取到的行程单数量和勾选的不一致");
                saveOver();
                return;
            }
            for (let key in GDRouteIds) {
                if (GDRouteIds.hasOwnProperty(key)) {
                    console.log("行程"+key + " : "+  GDRouteIds[key].startAddr + " - " + GDRouteIds[key].endAddr)
                }
            }
        }

        utils.toastInfo("保存完成，可以启动");
        saveOver();
        GSaveFlag = 2;
    });
});

// 悬浮按钮
var fw = floaty.window(
    <frame id="JmWindow">
        <button id="JmStartStopButton" w="30" h="30" bg="file://./img/start.png"/>
    </frame>
);
fw.setPosition(1, 1000);
fw.JmStartStopButton.setOnTouchListener(function(view, event){
    switch(event.getAction()){
        case event.ACTION_DOWN:
            x = event.getRawX();
            y = event.getRawY();
            windowX = fw.getX();
            windowY = fw.getY();
            downTime = new Date().getTime();
            return true;
        case event.ACTION_MOVE:
            //移动手指时调整悬浮窗位置
            fw.setPosition(1, windowY + (event.getRawY() - y));
            return true;
        case event.ACTION_UP:
            //手指弹起时如果偏移很小则判断为点击
            if(Math.abs(event.getRawY() - y) < 5 && Math.abs(event.getRawX() - x) < 5){
                startClick();
            }
            return true;
    }
    return true;
});
// 点击启动按钮，开始清单
ui.JmStart.click(function() {
    startClick()
});

function startClick(){
    threads.start(function() {
        if (GStop) {
            handleStart();
        } else {
            handleStop();
        }
    });
}

var GWsConn;
let wsIntervalId = 0;
function handleStart() {
    if (GSaveFlag != 2) {
        utils.toastInfo("请先保存之后再启动");
        return;
    }
    ui.JmStart.setClickable(false);
    fw.JmStartStopButton.setClickable(false);
    ui.JmStart.setText("启动中");
    console.info("启动中...");

    // 先关闭
    try {
        GWsConn.close(1000, "客户端主动关闭连接");
    } catch (e) {}
    // 开始抢单
    let res = startGet();
    if (res == false) {
        utils.toastErr("启动抢单失败");
        ui.JmStart.setClickable(true);
        fw.JmStartStopButton.setClickable(true);
        ui.JmStart.setText("启 动");
        fwIconStart();
        return
    }
    console.info("抢单开始");
    sleep(1000);
    GStop = false;
    ui.JmStart.setText("暂 停");
    ui.JmStart.setClickable(true);
    fw.JmStartStopButton.setClickable(true);
    fwIconStop();
    console.show(true);
}


function handleStop() {
    ui.JmStart.setClickable(false);
    fw.JmStartStopButton.setClickable(false);
    ui.JmStart.setText("暂停中");
    console.info("暂停中...");

    sleep(1000);
    // ws关闭链接
    clearInterval(wsIntervalId);
    try {
        GWsConn.close(1000, "客户端主动关闭连接");
    } catch (e) {
    }
    sleep(1000);
    GStop = true;
    console.info("抢单停止");
    ui.JmStart.setText("启 动");
    ui.JmStart.setClickable(true);
    fw.JmStartStopButton.setClickable(true);
    fwIconStart();
}

importClass(android.graphics.BitmapFactory);
importClass(android.graphics.drawable.BitmapDrawable);
var startBtnPath = files.path("img/start.png");
var stopBtnPath = files.path("img/stop.png");
var bitmapStart = BitmapFactory.decodeFile(startBtnPath);
var bitmapStop = BitmapFactory.decodeFile(stopBtnPath);
function fwIconStart() {
    threads.start(function() {
        ui.run(function() {
            //fw.JmStartStopButton.setBackgroundResource(android.R.drawable.ic_media_play);
            fw.JmStartStopButton.setBackgroundDrawable(new BitmapDrawable(context.getResources(), bitmapStart));
        })
    });
    
}
function fwIconStop() {
    threads.start(function() {
        ui.run(function() {
            // fw.JmStartStopButton.setBackgroundResource(android.R.drawable.ic_media_pause);
            fw.JmStartStopButton.setBackgroundDrawable(new BitmapDrawable(context.getResources(), bitmapStop));
        });
    })
}

function startGet() {
    if (PJYCode == "") {
        console.error("请填写正确的卡密")
        return false
    }
    let tk = GGToken.split("&*&");
    let phone = db.get(tk[1]);
    if (phone == "" || phone == undefined) {
        console.error("抢单TK已失效，请重新获取");
        return false
    }
    let data = {
        Phone: phone,
        GToken: GGToken,
        FToken: GFToken,
        GTokenFresh: GGTokenFresh,
        CarNum: GCarNum,
        DRute: {
            RouteIds: GDRouteIds,
            ShunLu: GShunLu,
            OrderBy: GDOrderBy,
            FromDist: GDFromDist,
            ToDist:   GDToDist,
            FreshMin: GDFreshMin,
            FreshMax: GDFreshMax,
        },
        BRute: {
            TabIds: GTabIds,
            FromDist: GBFromDist,
            ToDist:   0, // 暂时没有这个筛选项
            OrderBy: GBOrderBy,
            OrderDist: GBOrderDist,
            NeedCity: GNeedCity.split("#"),
            NotCity: GNotCity.split("#"),
            FreshMin: GBFreshMin,
            FreshMax: GBFreshMax,
        },
        CRute: {
            ApiType: GApiType,
            FInterval: GFInterval,
            FIntervalStop: GFIntervalStop,
            OrderType: GOrderType,
            HighWay: GHighWay,
            Date: GDate,
            StartH: GStartH,
            EndH: GEndH,
            MoneyMin: GMoneyMin,
            MoneyMax: GMoneyMax,
            Oneself: GOneself,
            OneselfMin: GOneselfMin,
            OneselfMax: GOneselfMax,
            Pinzuo: GPinzuo,
            PinzuoMin: GPinzuoMin,
            PinzuoMax: GPinzuoMax,
            Shushi: GShushi,
            ShushiMin: GShushiMin,
            ShushiMax: GShushiMax,
            AdCode: GAdCode,
            Lat: GLatL+"",
            Lng: GLngL+"",
        }
    }
    let param = "code=" + PJYCode + "&q=" + encodeURIComponent(JSON.stringify(data))+"&PJYToken="+db.get("PJYToken");
    try {GWsConn.close();} catch (e) {}
    try {
        GWsConn = FX.StartWs(param, GWsListener);
    } catch (e) {
        log(e)
        log("请大退重启或者切换网络重试")
        return false
    }
    return true;
}

let GWsListener = {
    onOpen: function (webSocket, response) {
        //打开链接后，想服务器端发送一条消息
        webSocket.send("start");
    },
    onMessage: function(webSocket, msg) {
        WsOnMsg(webSocket, msg);
    },
    onClosing: function (webSocket, code, response) {
        console.log("正在关闭中...")
    },
    onClosed: function (webSocket, code, response) {
        console.log("已关闭")
    },
    onFailure: function (webSocket, t, response) {
        handleStop();
        console.log(t);
        utils.toastErr("请大退重启或者切换网络重试");
        try {
            let bodyStr = response.body().string();
            let obj = JSON.parse(bodyStr);
            console.log(obj.msg);
            toast(obj.msg);
        } catch (e) {
        }
    }
}

function WsOnMsg(webSocket, msg) {
    let data = JSON.parse(msg);
    let type = parseInt(data["type"]);
    let body = data["body"];
    switch (type) {
        case 1:
            // 日志信息
            WsOnMsgConsole(body);
            break;
        case 2:
            // 抢单记录
            WsOnMsgRecord(body);
            break;
        case 10: 
            // 订单错误
            console.error(body);
            toast(body);
            handleStop();
            media.playMusic("./music/err.mp3", 0.8);
            break;
        case 11:
            // 抢单成功 - 停止
            console.error(body);
            toast(body);
            handleStop();
            media.playMusic("./music/successPk.mp3", 33);
            break
        case 20:
            if (body != "" && body != undefined) {
                utils.toastErr(body);
            }
            media.playMusic("./music/successPk.mp3", l);
            break
        case 21:
            if (body != "" && body != undefined) {
                utils.toastErr(body);   
            }
            media.playMusic("./music/err.mp3", 1);
            break
        case 22:
            if (body != "" && body != undefined) {
                utils.toastErr(body);
            }
            media.playMusic("./music/success.mp3", 1);
            break
    }
}

function WsOnMsgConsole(body) {
    let data  = body.data;
    for (i in data) {
        let item = data[i];
        let type = parseInt(item["type"]);
        switch (type) {
            case 1:
                console.log(item["msg"])
                break;
            case 2:
                console.info(item["msg"])
                break;
            case 3:
                console.error(item["msg"])
                break;
            case 4:
                console.warn(item["msg"])
                break;
            default:
                console.log(item["msg"])
                break;
        }
    }
}

function WsOnMsgRecord(body) {
    let oldText = ui.JmRecord.getText() + "";
    let text= "";
    for (let i in body) {
        text += body[i] + "\n";
    }
    ui.run(function() {
        ui.JmRecord.setText(oldText + "\n" + text);
        setTimeout(() => {
            ui.JmRecordScroll.fullScroll(View.FOCUS_DOWN);// 滑动到最低
        }, 200);
    })
}
importClass(android.content.BroadcastReceiver);
importClass(android.content.Intent);
importClass(android.content.Context);
importClass(android.app.PendingIntent);
importClass(android.provider.Settings);
importClass(android.net.Uri);
importClass(android.content.IntentFilter);
importClass(android.location.LocationManager);
importClass(android.location.Location);
importClass(android.location.LocationListener);
importClass(android.location.Criteria);
// var locationListener = new LocationListener() {
//     onLocationChanged(location) {
//      if(location!=null){
//       log("维度:"+location.getLatitude()+"\n经度:"+location.getLongitude());
//      }else{
//       log("获取不到数据");
//      }
//     }
// };
// 获取经纬度并设置城市信息
function setLoaction11(address) {
    if (address == "") {
        console.log("使用手机定位地址");
        var manager = context.getSystemService(Context.LOCATION_SERVICE);
        var criteria = new Criteria();
        criteria.setAccuracy(Criteria.ACCURACY_FINE); //定位精度: 最高
        criteria.setAltitudeRequired(true); //海拔信息：不需要
        criteria.setBearingRequired(true); //方位信息: 不需要
        criteria.setCostAllowed(true);  //是否允许付费
        criteria.setPowerRequirement(Criteria.POWER_LOW); //耗电量: 低功耗
        
        var provider = manager.getBestProvider(criteria, true); //获取GPS信息
        var location = manager.getLastKnownLocation(provider);
        // manager.requestLocationUpdates(provider, 2000, 5, locationListener);

        if (location == null) {
            utils.toastErr("未取到定位，请打开哈啰再试或者手动输入位置");
            return;
        }
        GLatL = location.getLatitude() + "";
        GLngL = location.getLongitude() + "";
        if (GLatL == undefined) {
            utils.toastErr("请确认GPS权限是否打开");
            return;
        }
        let cityInfo = FX.GetLocation(GLatL, GLngL);
        GAdCode = cityInfo["adCode"];
        GCityName = cityInfo["cityName"];
    } else {
        // 根据地址请求第三方接口获取经纬度
        console.log("请求第三方接口获取地址");
        address = address+""
        let res = FX.GetGeo(address);
        if (res == "") {
            return;
        }
        GLatL = res["lat"];
        GLngL = res["lng"];
        GAdCode = res["adCode"];
        GCityName = res["cityName"];
        db.put("GAddress", address);
    }
    setSeoS(GLatL, GLngL);
    db.put("GLatL", GLatL);
    db.put("GLngL", GLngL);
    db.put("GCityName", GCityName + "");
    db.put("GAdCode", GAdCode + "");
}

function setSeoS(lat, lng) {
    lat += ""
    lng += ""
    let a1 = lat.split(".");
    GLatS = a1[0] + "." + a1[1].substring(0, 6);
    let b1 = lng.split(".");
    GLngS = b1[0] + "." + b1[1].substring(0, 6);
    console.log(GLatS, GLngS);
}


// 获取经纬度并设置城市信息
function setLoaction(address) {
    if (address == "") {
        // console.log("使用本地地址");
        importClass(android.location.LocationManager);
        importClass(android.location.Location);
        importClass(android.location.Criteria);
        Context = android.content.Context;
        var manager = context.getSystemService(Context.LOCATION_SERVICE);
        var criteria = new Criteria();
        criteria.setAccuracy(Criteria.ACCURACY_FINE);
        var provider = manager.getBestProvider(criteria, true);
        var location = manager.getLastKnownLocation(provider);
        var location = manager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER);
        if (location == null) {
            location = manager.getLastKnownLocation(LocationManager.PASSIVE_PROVIDER);
        }
        if (location == null) {
            utils.toastErr("未取到定位，请打开哈啰再试或者手动输入位置");
            return;
        }
        GLatL = location.getLatitude() + "";
        GLngL = location.getLongitude() + "";
        if (GLatL == undefined) {
            utils.toastErr("请确认GPS权限是否打开");
            return;
        }
        let cityInfo = FX.GetLocation(GLatL, GLngL);
        GAdCode = cityInfo["adCode"];
        GCityName = cityInfo["cityName"];
        address = cityInfo["address"];
    } else {
        // 根据地址请求第三方接口获取经纬度
        // console.log("请求第三方接口获取地址");
        address = address+""
        if (db.get("GAddress") != undefined && db.get("GAddress")+"" == address) {
            GLatL = db.get("GLatL") + "";
            GLngL = db.get("GLngL") + "";
            GAdCode = db.get("GAdCode") + "";
            GCityName = db.get("GCityName") + "";
        } else {
            let res = FX.GetGeo(address);
            if (res == "") {
                return;
            }
            GLatL = res["lat"];
            GLngL = res["lng"];
            GAdCode = res["adCode"];
            GCityName = res["cityName"];
        }
        db.put("GAddress", address);
    }
    setSeoS(GLatL, GLngL);
    db.put("GLatL", GLatL);
    db.put("GLngL", GLngL);
    db.put("GAdCode", GAdCode + "");
    db.put("GCityName", GCityName + "");
    console.log("当前位置：" + address);
}

function getDate() {
    var currentDate = new Date();
    GYear = currentDate.getFullYear();
    GMonth = currentDate.getMonth() + 1;
    GDay = currentDate.getDate();
    var hours = currentDate.getHours();
    var minutes = currentDate.getMinutes();
    var seconds = currentDate.getSeconds();
    return GYear + "-" + GMonth + "-" + GDay;
}
