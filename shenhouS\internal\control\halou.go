package control

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/limitzhang87/shenhouS/internal/dao"
	"github.com/limitzhang87/shenhouS/internal/grab"
	"github.com/limitzhang87/shenhouS/internal/utils"
	"github.com/limitzhang87/shenhouS/internal/websocket"
	"net/http"
	"strings"
	"time"
)

func StartWs(c *gin.Context) {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println(err)
			c.JSON(http.StatusOK, gin.H{"errno": -1, "msg": "服务出现异常:" + err.(error).Error()})
		}
	}()
	q := c.Query("q")
	var req *dao.GradReq
	err := json.Unmarshal([]byte(q), &req)
	if err != nil {
		fmt.Println(err)
		c.<PERSON>(http.StatusOK, gin.H{"errno": -1, "msg": "提交的抢单任务参数错误"})
		return
	}
	req.BRute.NeedCity = utils.TrimArrayStr(req.BRute.NeedCity)
	req.BRute.NotCity = utils.TrimArrayStr(req.BRute.NotCity)
	req.StartTime = time.Now()
	req.IsPass12Check = false

	// 校验车辆信息和PJY卡密
	code := c.Query("code")
	if !strings.Contains(code, "smbb") {
		c.JSON(http.StatusOK, gin.H{"errno": -1, "msg": "卡密不正确"})
		return
	}

	ws, err := websocket.NewWsHandler(c)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{"errno": -1, "msg": "创建websocket失败"})
		return
	}
	defer ws.Close()

	userInfo, err := grab.GetUserInfo(req.GToken)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{"errno": -1, "msg": "获取司机信息失败"})
		return
	}
	req.UserInfo = userInfo

	//testWs(ws)
	go func() {
		g := grab.NewGradHandler(ws, req, code)
		g.Run()
	}()
	ws.Run()
}

func testWs(ws *websocket.Handler) {
	go func() {
		ticker := time.NewTicker(time.Millisecond * time.Duration(700))
		defer func() {
			ticker.Stop()
			fmt.Println("业务关闭写入")
		}()
	FOR:
		for {
			select {

			case <-ticker.C:
				fmt.Println("发送")
				curTime := time.Now().Format("2006-01-02 15:04:05")
				msg := []string{
					curTime + " 1",
					curTime + " 2",
					curTime + " 3",
					curTime + " 4",
					curTime + " 5",
					curTime + " 6",
					curTime + " 7",
				}
				data := make([]dao.WsBodyConsoleData, 0, len(msg))
				for _, v := range msg {
					data = append(data, dao.WsBodyConsoleData{
						Type: dao.WsConsoleTypeInfo,
						Msg:  v,
					})
				}

				dataByte, _ := json.Marshal(dao.BuildWsConsole(data))
				ws.Send(dataByte)

				body := dao.WsData{
					Type: dao.WsDataTypeRecord,
					Body: []string{"1", "2", "3", "4"},
				}
				dataByte, _ = json.Marshal(body)
				ws.Send(dataByte)
			case <-ws.StopChan:
				break FOR
			}
		}
	}()
}
