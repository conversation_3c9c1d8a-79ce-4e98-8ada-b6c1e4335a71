package control

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/limitzhang87/shenhouS/internal/dao"
	"github.com/limitzhang87/shenhouS/internal/grab"
	"github.com/limitzhang87/shenhouS/internal/utils"
	"github.com/limitzhang87/shenhouS/internal/websocket"
	"net/http"
	"strings"
	"time"
)

func StartWs(c *gin.Context) {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println(err)
			c.JSON(http.StatusOK, gin.H{"errno": -1, "msg": "服务出现异常:" + err.(error).Error()})
		}
	}()
	q := c.Query("q")
	var req *dao.GradReq
	err := json.Unmarshal([]byte(q), &req)
	if err != nil {
		fmt.Println(err)
		c.<PERSON>(http.StatusOK, gin.H{"errno": -1, "msg": "提交的抢单任务参数错误"})
		return
	}
	req.BRute.NeedCity = utils.TrimArrayStr(req.BRute.NeedCity)
	req.BRute.NotCity = utils.TrimArrayStr(req.BRute.NotCity)
	req.StartTime = time.Now()
	req.IsPass12Check = false

	// 校验车辆信息和PJY卡密
	code := c.Query("code")
	if !strings.Contains(code, "smbb") {
		c.JSON(http.StatusOK, gin.H{"errno": -1, "msg": "卡密不正确"})
		return
	}

	ws, err := websocket.NewWsHandler(c)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{"errno": -1, "msg": "创建websocket失败"})
		return
	}
	defer ws.Close()

	userInfo, err := grab.GetUserInfo(req.GToken)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{"errno": -1, "msg": "获取司机信息失败"})
		return
	}
	req.UserInfo = userInfo

	//testWs(ws)
	go func() {
		g := grab.NewGradHandler(ws, req, code)
		g.Run()
	}()

	// 启动消息处理协程
	go handleWebSocketMessages(ws)

	ws.Run()
}

// handleWebSocketMessages 处理WebSocket消息
func handleWebSocketMessages(ws *websocket.Handler) {
	defer func() {
		if err := recover(); err != nil {
			fmt.Printf("WebSocket消息处理异常: %v\n", err)
		}
	}()

	for {
		select {
		case <-ws.CheckStop():
			fmt.Println("WebSocket消息处理停止")
			return
		case msg, ok := <-ws.ReadChan:
			if !ok {
				fmt.Println("WebSocket读取通道关闭")
				return
			}

			// 处理接收到的消息
			handleClientMessage(msg)
		}
	}
}

// handleClientMessage 处理客户端消息
func handleClientMessage(msg []byte) {
	defer func() {
		if err := recover(); err != nil {
			fmt.Printf("处理客户端消息异常: %v\n", err)
		}
	}()

	var data map[string]interface{}
	if err := json.Unmarshal(msg, &data); err != nil {
		fmt.Printf("解析客户端消息失败: %v\n", err)
		return
	}

	msgType, ok := data["type"].(string)
	if !ok {
		fmt.Println("消息类型无效")
		return
	}

	switch msgType {
	case "token_refresh_response":
		handleTokenRefreshResponse(data)
	default:
		fmt.Printf("未知消息类型: %s\n", msgType)
	}
}

// handleTokenRefreshResponse 处理token刷新响应
func handleTokenRefreshResponse(data map[string]interface{}) {
	defer func() {
		if err := recover(); err != nil {
			fmt.Printf("处理token刷新响应异常: %v\n", err)
		}
	}()

	responseData, ok := data["data"].(map[string]interface{})
	if !ok {
		fmt.Println("token刷新响应数据格式错误")
		return
	}

	success, _ := responseData["success"].(bool)
	if !success {
		fmt.Println("token刷新失败")
		return
	}

	oldToken, _ := responseData["old_token"].(string)
	newToken, _ := responseData["new_token"].(string)

	if oldToken == "" || newToken == "" {
		fmt.Println("token刷新响应中token为空")
		return
	}

	// 更新token缓存
	grab.SetNewToken(oldToken, newToken)
	fmt.Printf("收到token刷新响应: %s -> %s\n", oldToken[:20]+"...", newToken[:20]+"...")
}

func testWs(ws *websocket.Handler) {
	go func() {
		ticker := time.NewTicker(time.Millisecond * time.Duration(700))
		defer func() {
			ticker.Stop()
			fmt.Println("业务关闭写入")
		}()
	FOR:
		for {
			select {

			case <-ticker.C:
				fmt.Println("发送")
				curTime := time.Now().Format("2006-01-02 15:04:05")
				msg := []string{
					curTime + " 1",
					curTime + " 2",
					curTime + " 3",
					curTime + " 4",
					curTime + " 5",
					curTime + " 6",
					curTime + " 7",
				}
				data := make([]dao.WsBodyConsoleData, 0, len(msg))
				for _, v := range msg {
					data = append(data, dao.WsBodyConsoleData{
						Type: dao.WsConsoleTypeInfo,
						Msg:  v,
					})
				}

				dataByte, _ := json.Marshal(dao.BuildWsConsole(data))
				ws.Send(dataByte)

				body := dao.WsData{
					Type: dao.WsDataTypeRecord,
					Body: []string{"1", "2", "3", "4"},
				}
				dataByte, _ = json.Marshal(body)
				ws.Send(dataByte)
			case <-ws.StopChan:
				break FOR
			}
		}
	}()
}
