package pjy

import (
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"github.com/limitzhang87/shenhouS/internal/utils"
	"sort"
	"strconv"
	"strings"
	"time"
)

const (
	AppKey    = "csq55njdquspgbckl010"
	AppSecret = "G8WvQJyxTPa3bi3mRmJScJD832uOROqk"
	HOST      = "api.paojiaoyun.com"
	PROTOCOL  = "http"
)

type PJY struct {
	AppKey    string
	AppSecret string
	HTTP      *utils.HTTPClient
}

func NewPJY(key, secret string) *PJY {
	return &PJY{
		AppKey:    key,
		AppSecret: secret,
		HTTP:      utils.NewHTTPClient(utils.GetProxyIpHttps()),
	}
}

func (p *PJY) GetTimestamp() int64 {
	return time.Now().Unix()
	//urlPath := "http://api.m.taobao.com/rest/api3.do?api=mtop.common.getTimestamp"
	//res, _, err := p.HTTP.Get(urlPath, nil)
	//if err != nil {
	//	fmt.Println("请求卡密配置失败", err)
	//	return time.Now().Unix()
	//}
	//var rsp map[string]interface{}
	//err = json.Unmarshal([]byte(res), &rsp)
	//if err != nil {
	//	return time.Now().Unix()
	//}
	//
	//data, ok := rsp["data"].(map[string]interface{})
	//if !ok {
	//	return time.Now().Unix()
	//}
	//
	//t, ok := data["t"].(float64)
	//if !ok {
	//	return time.Now().Unix()
	//}
	//tt := int64(t)
	//tt = tt / 1000
	//if tt <= 0 {
	//	return time.Now().Unix()
	//}
	//return tt
}

func (p *PJY) CardConfig(card string) (*CommonRsp, error) {
	path := "/v1/card/config"
	urlPath := HOST + path
	uuidObj, _ := uuid.NewUUID()
	params := map[string]string{
		"app_key":   p.AppKey,
		"card":      card,
		"nonce":     uuidObj.String(),
		"timestamp": fmt.Sprint(p.GetTimestamp() - 3),
	}
	paramsStr := p.joinParams(params)
	temp := "GET" + HOST + path + paramsStr + AppSecret
	sign := fmt.Sprintf("%x", md5.Sum([]byte(temp)))
	paramsStr += "&sign=" + sign

	urlPath = PROTOCOL + "://" + HOST + path + "?" + paramsStr

	res, _, err := p.HTTP.Get(urlPath, nil)
	if err != nil {
		fmt.Println("请求卡密配置失败", err)
		return nil, err
	}
	fmt.Println("PJY配置返回", res)
	rsp := p.handleCommonRsp(res)
	return rsp, err
}

func (p *PJY) CheckValid(card string) bool {
	rsp, _ := p.CardConfig(card)
	if rsp.Code == 404 {
		return false
	}
	return true
}

func (p *PJY) GetRemoveVar(card, key, token string) (string, error) {
	path := "/v1/af/remote_var"
	urlPath := HOST + path
	uuidObj := uuid.New()
	params := map[string]string{
		"app_key":   p.AppKey,
		"card":      card,
		"key":       key,
		"nonce":     uuidObj.String(),
		"timestamp": strconv.FormatInt(time.Now().Unix(), 10),
		"token":     token,
	}

	paramsStr := p.joinParams(params)
	temp := "GET" + HOST + path + paramsStr + AppSecret
	sign := fmt.Sprintf("%x", md5.Sum([]byte(temp)))
	paramsStr += "&sign=" + sign

	urlPath = PROTOCOL + "://" + HOST + path + "?" + paramsStr
	fmt.Println(urlPath)
	res, _, err := p.HTTP.Get(urlPath, nil)
	if err != nil {
		return "", errors.New("请求泡椒云失败")
	}
	fmt.Println(res)

	var rsp CommonRsp
	err = json.Unmarshal([]byte(res), &rsp)
	if err != nil {
		return "", errors.New("请求泡椒云失败")
	}

	value, _ := rsp.Result["value"].(string)

	return value, nil
}

func (p *PJY) CheckConfigErr(card, config string) bool {
	// 获取远程数据
	rsp, err := p.CardConfig(card)
	if err != nil {
		return true
	}

	if rsp.Code == 404 {
		fmt.Println("CheckConfigErr", 404, rsp.Message)
		return false
	}
	if rsp.Code != 0 {
		fmt.Println("CheckConfigErr", rsp.Code, rsp.Message)
		return true
	}
	removeConf, _ := rsp.Result["config"].(string)
	fmt.Println("CheckConfigErr", 0, removeConf, config)
	return removeConf == config
}

func (p *PJY) joinParams(params map[string]string) string {
	var keys []string
	for key := range params {
		keys = append(keys, key)
	}

	sort.Strings(keys)

	var encodedParams []string
	for _, key := range keys {
		value := params[key]
		encodedParams = append(encodedParams, fmt.Sprintf("%s=%s", key, value))
	}
	return strings.Join(encodedParams, "&")
}

func (p *PJY) handleRsp(str string) *Rsp {
	var rsp Rsp
	err := json.Unmarshal([]byte(str), &rsp)
	if err != nil {
		return &Rsp{
			Code:    -1,
			Message: "unmarshal rsp fail",
		}
	}
	return &rsp
}

func (p *PJY) handleCommonRsp(str string) *CommonRsp {
	var rsp CommonRsp
	err := json.Unmarshal([]byte(str), &rsp)
	if err != nil {
		fmt.Println("解析错误", str, err)
		return &CommonRsp{
			Code:    -1,
			Message: "unmarshal rsp fail",
		}
	}
	return &rsp
}

type Rsp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	//Result  struct {
	//	Config string `json:"config"`
	//} `json:"result"`
	//Nonce string `json:"nonce"`
	//Sign  string `json:"sign"`
}

type CommonRsp struct {
	Code    int                    `json:"code"`
	Message string                 `json:"message"`
	Result  map[string]interface{} `json:"result"`
}
