package utils

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"embed"
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/dop251/goja"
)

var AES *AesUtils

type AesUtils struct {
	vm *goja.Runtime
}

const (
	AESKey = "0199bec97dfa5e0d"
	AESIV  = "0199bec97dfa5e0d"
)

var dataFs embed.FS

func InitJsFile(fs embed.FS) {
	dataFs = fs
}

func NewAesUtils() (*AesUtils, error) {
	jsencrypt := getJsContent()
	vm := goja.New()
	_, err := vm.RunString(jsencrypt)
	if err != nil {
		return nil, errors.New(err.Error() + ": 执行rsa.js失败")
	}
	return &AesUtils{vm}, nil
}

func getJsContent() string {
	//dir, err := os.Getwd()
	//if err != nil {
	//	return ""
	//}
	//index := strings.Index(dir, "shenhouS")
	//if index == -1 {
	//	return ""
	//}
	//basePath := dir[:index+8]
	//jsPath := filepath.Join(basePath, "public", "haluo_aes.js")
	//jsencrypt, err := os.ReadFile(jsPath)
	//if err != nil {
	//	fmt.Println(err)
	//	return ""
	//}
	//return string(jsencrypt)

	jsencrypt, err := dataFs.ReadFile("public/haluo_aes.js")
	if err != nil {
		fmt.Println(err)
		return ""
	}
	return string(jsencrypt)
}

func (a *AesUtils) Encrypt(data string) (string, error) {
	script := `var res = AES_Encrypt('%s')`
	script = fmt.Sprintf(script, data)
	_, err := a.vm.RunString(script)
	if err != nil {
		return "", errors.New(err.Error() + ": 执行加密错误")
	}
	return a.vm.Get("res").String(), nil
}

func (a *AesUtils) Decrypt(data string) (string, error) {
	script := `var res = AES_Decrypt('%s')`
	script = fmt.Sprintf(script, data)
	_, err := a.vm.RunString(script)
	if err != nil {
		return "", errors.New(err.Error() + ": 执行解密错误")
	}
	return a.vm.Get("res").String(), nil
}

func AesEncrypt(plaintext []byte) (string, error) {
	block, err := aes.NewCipher([]byte(AESKey))
	if err != nil {
		return "", err
	}
	paddedPlaintext := pkcs7Padding(plaintext, block.BlockSize())
	ciphertext := make([]byte, len(paddedPlaintext))
	mode := cipher.NewCBCEncrypter(block, []byte(AESIV))
	mode.CryptBlocks(ciphertext, paddedPlaintext)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func AesDecrypt(ciphertext string) (string, error) {
	block, err := aes.NewCipher([]byte(AESKey))
	if err != nil {
		return "", err
	}
	decodedCiphertext, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}
	decryptedData := make([]byte, len(decodedCiphertext))
	mode := cipher.NewCBCDecrypter(block, []byte(AESIV))
	mode.CryptBlocks(decryptedData, decodedCiphertext)
	return string(pkcs7Unpadding(decryptedData)), nil
}

func AesEncryptNew(key string, plaintext []byte) (string, error) {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}
	paddedPlaintext := pkcs7Padding(plaintext, block.BlockSize())
	ciphertext := make([]byte, len(paddedPlaintext))
	mode := cipher.NewCBCEncrypter(block, []byte(key))
	mode.CryptBlocks(ciphertext, paddedPlaintext)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func AesDecryptNew(key string, ciphertext string) (string, error) {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "nil", err
	}
	decodedCiphertext, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}
	decryptedData := make([]byte, len(decodedCiphertext))
	mode := cipher.NewCBCDecrypter(block, []byte(key))
	mode.CryptBlocks(decryptedData, decodedCiphertext)
	return string(pkcs7Unpadding(decryptedData)), nil
}

// 使用PKCS7填充方式对数据进行填充
func pkcs7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - (len(data) % blockSize)
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

// 对使用PKCS7填充方式的数据进行去填充
func pkcs7Unpadding(data []byte) []byte {
	length := len(data)
	unpadding := int(data[length-1])
	return data[:(length - unpadding)]
}
