package websocket

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"net/http"
	"sync"
	"time"
)

type Handler struct {
	C           *gin.Context
	Conn        *websocket.Conn
	StopChan    chan struct{}
	<PERSON><PERSON>han    chan []byte
	SendChan    chan []byte
	Errno       int
	Msg         string
	CloseOnce   sync.Once
	WaitSendEnd sync.WaitGroup
}

var upGrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
}

func NewWsHandler(c *gin.Context) (*Handler, error) {
	// 获取WebSocket连接
	conn, err := upGrader.Upgrade(c.<PERSON>, c.Request, nil)
	if err != nil {
		fmt.Println("创建ws失败", err)
		return nil, err
	}

	return &Handler{
		C:        c,
		Conn:     conn,
		<PERSON><PERSON>han: make(chan struct{}),
		ReadChan: make(chan []byte, 100),
		Send<PERSON>han: make(chan []byte, 100),
		Errno:    -1,
		Msg:      "非常正关闭",
	}, nil
}

func (h *Handler) Close() {
	defer func() {
		if r := recover(); r != nil {
			fmt.Println("Conn already closed")
		}
	}()

	//time.Sleep(time.Second * time.Duration(3))

	h.WaitSendEnd.Wait()
	// 等待一段时间以确保客户端接收到关闭帧
	time.Sleep(time.Second * time.Duration(5))
	_ = h.Conn.Close()
	return
}

func (h *Handler) Run() {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println(err)
		}
	}()

	h.ListenRead() // 异步监听数据
	h.ListenSend() // 异步写入数据
	h.WaitStop()   // 阻塞等待ws
	fmt.Println("WS执行结束")
}

func (h *Handler) Stop() {
	defer func() {
		if r := recover(); r != nil {
			fmt.Println("Channel already closed")
		}
	}()
	h.CloseOnce.Do(func() {
		fmt.Println("关闭所有通道")
		close(h.StopChan)
		close(h.ReadChan)
		close(h.SendChan)
	})
}

func (h *Handler) CheckStop() <-chan struct{} {
	return h.StopChan
}
func (h *Handler) IsStop() bool {
	select {
	case _, ok := <-h.StopChan:
		// 如果 ok 为 false，说明 channel 已经关闭
		return !ok
	default:
		// 如果 channel 还未关闭，返回 false
		return false
	}
}

func (h *Handler) WaitStop() struct{} {
	return <-h.CheckStop()
}

func (h *Handler) ListenRead() {
	go func() {
		defer func() {
			// 读取失败，相当于断开连接
			if recover() != nil {
				h.Stop()
			}
			fmt.Println("监听读取数据关闭")
		}()
	FOR:
		for {
			select {
			case <-h.StopChan:
				break FOR
			default:
				messageType, p, err := h.Conn.ReadMessage()
				if err != nil {
					if websocket.IsCloseError(err, websocket.CloseNormalClosure) {
						fmt.Println("客户端关闭连接")
					} else if websocket.IsCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
						fmt.Println("客户端异常关闭连1")
					} else {
						fmt.Println("异常关闭", err)
					}
					h.Stop()
					break FOR
				}
				fmt.Println("type:", messageType, "  msg:", string(p))
				h.ReadChan <- p // 写入到读通道中，有业务自己去赌
				//switch messageType {
				//case websocket.TextMessage:
				//	log.Println("Received Text Message:", string(p))
				//case websocket.BinaryMessage:
				//	log.Println("Received Binary Message")
				//case websocket.CloseMessage:
				//	log.Println("Received Close Message")
				//case websocket.PingMessage:
				//	log.Println("Received Ping Message")
				//case websocket.PongMessage:
				//	log.Println("Received Pong Message")
				//default:
				//	log.Println("Received Unknown Message Type")
				//}
			}
		}
	}()
}

func (h *Handler) ListenSend() {
	h.WaitSendEnd.Add(1)
	// 写入
	go func() {
		defer func() {
			fmt.Println("监听写入数据关闭")
			h.WaitSendEnd.Done()
		}()
	FOR:
		for {
			select {
			//case <-h.StopChan:
			//	break FOR
			case msg, ok := <-h.SendChan:
				if !ok {
					// 通道已经关闭，并且读书读取完了
					break FOR
				}
				// 输出WebSocket消息内容
				_ = h.Conn.WriteMessage(1, msg)
			}
		}
	}()
}

func (h *Handler) Send(msg []byte) {
	defer func() {
		if r := recover(); r != nil {
			fmt.Println("send on closed channel")
		}
	}()
	select {
	case <-h.StopChan:
		return
	case h.SendChan <- msg:
		return
	}
}
