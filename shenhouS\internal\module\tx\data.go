package tx

type GeoRsp struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
	//RequestID string `json:"request_id"`
	Result Result `json:"result"`
}
type Result struct {
	Title    string   `json:"title"`
	Location Location `json:"location"`
	AdInfo   AdInfo   `json:"ad_info"`
	//AddressComponents AddressComponents `json:"address_components"`
	Similarity  float64 `json:"similarity"`
	Deviation   int     `json:"deviation"`
	Reliability int     `json:"reliability"`
	Level       int     `json:"level"`
}

type Location struct {
	Lng float64 `json:"lng"`
	Lat float64 `json:"lat"`
}

type AdInfo struct {
	AdCode string `json:"adcode"`
}

type AddressComponents struct {
	Province     string `json:"province"`
	City         string `json:"city"`
	District     string `json:"district"`
	Street       string `json:"street"`
	StreetNumber string `json:"street_number"`
}
