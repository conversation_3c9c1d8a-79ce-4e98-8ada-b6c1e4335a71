package grab

import (
	"encoding/json"
	"github.com/limitzhang87/shenhouS/internal/dao"
	"github.com/limitzhang87/shenhouS/internal/websocket"
	"testing"
)

func TestGradHandler_GetValidOrder(t *testing.T) {
	reqStr := `{"GToken":"MTczMjQ1NjE1NA==.ash7waXvanNacQdz3Yok5E4q3HflHUyA+IRC5586Z9g=&*&45636e3750eb420fbd7a4cafe7d0b2cd","FToken":"MTczMjQ1NjE1NA==.ash7waXvanNacQdz3Yok5E4q3HflHUyA+IRC5586Z9g=&*&45636e3750eb420fbd7a4cafe7d0b2cd","GTokenFresh":true,"CarNum":"云G20C13","DRute":{"RouteIds":{},"ShunLu":30,"OrderBy":4,"FromDist":1000,"ToDist":11100,"FreshMin":3000,"FreshMax":3001},"BRute":{"TabIds":[3],"FromDist":300,"ToDist":0,"OrderBy":0,"OrderDist":9991,"NeedCity":[""],"NotCity":[""],"FreshMin":3000,"FreshMax":3001},"CRute":{"ApiType":0,"FInterval":0,"FIntervalStop":0,"OrderType":0,"HighWay":false,"Date":2,"StartH":1,"EndH":21,"MoneyMin":10000,"MoneyMax":111111,"Oneself":true,"OneselfMin":1,"OneselfMax":4,"Pinzuo":true,"PinzuoMin":1,"PinzuoMax":4,"Shushi":true,"ShushiMin":1,"ShushiMax":4,"AdCode":"460105","Lat":"20.011804666166668","Lng":"110.19680794683333"}}`
	var req dao.GradReq
	_ = json.Unmarshal([]byte(reqStr), &req)

	req.CRute.MoneyMin = 1
	req.CRute.MoneyMax = 10000
	req.CRute.StartH = 1
	req.CRute.EndH = 22

	//req.BRute.NeedCity = []string{"玉林"}
	req.BRute.NotCity = []string{"玉林"}

	rspStr := `{"code":0,"data":{"startTime":"1724682038312","freeSeat":4,"list":[{"journeyId":"JP2024082652700600001209633230","passengerId":"1209633230","passengerGuid":"dc4aa5f015c0430c896b9b2eeced90b1","passengerName":"尾号1479","journeyType":1,"rating":"5.0","passengerCount":1,"startTime":"1724720700000","isCarPool":1,"startAddr":{"lon":"110.************","lat":"20.0079726905166","longAddr":"海南省海口市琼山区环湖路17号","shortAddr":"海口市琼山第二中学-西南门","cityCode":"0898","newCityCode":"0898","adCode":"460107","adName":"琼山区","cityName":"海口市","bizArea":"国兴"},"endAddr":{"lon":"110.44406","lat":"21.570949","longAddr":"广东省茂名市化州市笪桥镇","shortAddr":"朱砂村","cityCode":"0668","newCityCode":"0668","adCode":"440982","adName":"化州市","cityName":"茂名市","bizArea":"笪桥镇"},"startDistance":"18084","price":12710,"tipPrice":0,"avatarIndex":107,"orderStatus":10,"distance":223200,"driverMileagePrice":12710,"ruleUrl":"https://m2.hellobike.com/AppHitchMainH5/latest/index.html#/valuation-rule?cityCode=0898&startAdCode=460107&endCityCode=0668&endAdCode=440982&crossCity=1&planStartTime=1724720400000&refineIds=2691776642082002590,2780947619272720747,2795808996397762621,2795884450383266591,2795808996397762655,2691776642082002609,2780947619272720798,2795886872746461481,2795884450383266596,2780947619272720764,2795808996397762689,2691776642082002571,2780947619272720730,2795886872746461484,2795808996397762638,2795886872746461478,2795884450383266601&priceNo=PP2024082660460100000011209633230","driverProPoolFarePrice":12710,"bounty":0,"driverDiscount":0,"totalBounty":0,"startPlanStartTime":1724720400000,"endPlanStartTime":1724721000000,"orderTimes":5,"tollRule":"*跨城订单优先高速路线，高速费等相关费用由车主承担","prePayPriority":false,"passengerProPayPrice":16090,"goodsInfo":{"driverDispatchingFee":0},"bearHighwayFeeType":2,"highwayOrder":true,"driverHighwayFeeViewTag":"不承担高速费","instantType":5,"comfortType":0,"comfortExtraFee":11170,"agentType":0,"endPositionName":"笪桥镇","endPoiName":"笪桥镇","hasPoolFixdPrice":true,"deputyText":"","carPoolText":"拼座","requirements":[[{"text":"乘客不承担高速费","color":"#5A6066","fontSize":12}]],"planArriveTime":1724720700000,"departureSoon":false,"valueAddedPrice":0},{"journeyId":"JP2024082655590600001738825965","passengerId":"1738825965","passengerGuid":"3a70339191b94196a3ac7e792251f552","passengerName":"尾号6817","journeyType":1,"rating":"5.0","passengerCount":2,"startTime":"1724724600000","isCarPool":1,"startAddr":{"lon":"109.90710264176738","lat":"19.657216600040336","longAddr":"海南省澄迈县中兴镇","shortAddr":"海南省澄迈县中兴镇","cityCode":"0804","newCityCode":"0804","adCode":"469023","adName":"澄迈县","cityName":"澄迈县","bizArea":"中兴镇","poiId":""},"endAddr":{"lon":"110.80531243763","lat":"21.************","longAddr":"茂名市-高州市","shortAddr":"大兴园","cityCode":"0668","newCityCode":"0668","adCode":"440981","adName":"高州市","cityName":"茂名市","bizArea":"石鼓镇","poiId":""},"startDistance":"73098","price":28890,"tipPrice":0,"avatarIndex":107,"orderStatus":10,"distance":318500,"driverMileagePrice":28890,"ruleUrl":"https://m2.hellobike.com/AppHitchMainH5/latest/index.html#/valuation-rule?cityCode=0804&startAdCode=469023&endCityCode=0668&endAdCode=440981&crossCity=1&planStartTime=1724724000000&refineIds=2780947619272721951,2795808996397763893,2780947619272721934,2795808996397763825,2795808996397763859,2795808996397763842,2780947619272722002,2780947619272721968&priceNo=PP2024082634155700000011738825965","driverProPoolFarePrice":28890,"bounty":0,"driverDiscount":0,"totalBounty":0,"startPlanStartTime":1724724000000,"endPlanStartTime":1724725200000,"orderTimes":14,"tollRule":"*跨城订单优先高速路线，高速费等相关费用由车主承担","prePayPriority":true,"passengerProPayPrice":31090,"goodsInfo":{"driverDispatchingFee":0},"hasThroughHighway":true,"bearHighwayFeeType":3,"highwayOrder":true,"driverHighwayFeeViewTag":"愿意协商高速费","instantType":5,"comfortType":0,"comfortExtraFee":24110,"agentType":0,"endPositionName":"石鼓镇","endPoiName":"石鼓镇","hasPoolFixdPrice":true,"deputyText":"","carPoolText":"拼座","requirements":[[{"text":"乘客愿意协商高速费","color":"#5A6066","fontSize":12}]],"planArriveTime":1724724600000,"departureSoon":false,"valueAddedPrice":0},{"journeyId":"JP2024082654267900001738825965","passengerId":"1738825965","passengerGuid":"3a70339191b94196a3ac7e792251f552","passengerName":"尾号6817","journeyType":1,"rating":"5.0","passengerCount":2,"startTime":"1724720700000","isCarPool":1,"startAddr":{"lon":"109.9032567","lat":"19.6558312","longAddr":"海南省澄迈县澄迈县","shortAddr":"澄迈裕农蜜柚种植专业合作社水晶蜜柚种植基地-东侧","cityCode":"0804","newCityCode":"0804","adCode":"469023","adName":"澄迈县","cityName":"澄迈县","bizArea":"中兴镇"},"endAddr":{"lon":"110.805979","lat":"21.808826","longAddr":"广东省茂名市高州市石鼓镇","shortAddr":"大兴园","cityCode":"0668","newCityCode":"0668","adCode":"440981","adName":"高州市","cityName":"茂名市","bizArea":"石鼓镇"},"startDistance":"72803","price":28920,"tipPrice":0,"avatarIndex":107,"orderStatus":10,"distance":318800,"driverMileagePrice":28920,"ruleUrl":"https://m2.hellobike.com/AppHitchMainH5/latest/index.html#/valuation-rule?cityCode=0804&startAdCode=469023&endCityCode=0668&endAdCode=440981&crossCity=1&planStartTime=1724720400000&refineIds=2795808996397763893,2780947619272721934,2795808996397763825,2795808996397763859,2795884450383269503,2691776642082003668,2756135438572852103,2756135438572852091,2780947619272721951,2795884450383269508,2756135438572852088,2795808996397763842,2795886872746463331,2691776642082003687,2795886872746463328,2780947619272722002,2795886872746463334,2780947619272721968,2795884450383269513,2691776642082003649&priceNo=PP2024082631652900000011738825965","driverProPoolFarePrice":28920,"bounty":0,"driverDiscount":0,"totalBounty":0,"startPlanStartTime":1724720400000,"endPlanStartTime":1724721000000,"orderTimes":14,"tollRule":"*跨城订单优先高速路线，高速费等相关费用由车主承担","prePayPriority":false,"passengerProPayPrice":38110,"goodsInfo":{"driverDispatchingFee":0},"bearHighwayFeeType":3,"highwayOrder":true,"driverHighwayFeeViewTag":"愿意协商高速费","instantType":5,"comfortType":0,"comfortExtraFee":24130,"agentType":0,"endPositionName":"石鼓镇","endPoiName":"石鼓镇","hasPoolFixdPrice":true,"deputyText":"","carPoolText":"拼座","requirements":[[{"text":"乘客愿意协商高速费","color":"#5A6066","fontSize":12}]],"planArriveTime":1724720700000,"departureSoon":false,"valueAddedPrice":0},{"journeyId":"JP2024082665696800001925310797","passengerId":"1925310797","passengerGuid":"5f5b09b108804d2da66ac897cc157be6","passengerName":"尾号4516","journeyType":1,"rating":"5.0","passengerCount":1,"startTime":"1724749800000","isCarPool":1,"startAddr":{"lon":"110.28004","lat":"19.997979","longAddr":"海口市秀英街道秀英大道55号附1号","shortAddr":"海口风尚派对酒店(海南省人民医院店)","cityCode":"0898","newCityCode":"0898","adCode":"460105","adName":"秀英区","cityName":"海口市","bizArea":"秀英街道"},"endAddr":{"lon":"110.92625","lat":"22.354034","longAddr":"茂名市东镇街道城西长运路60号","shortAddr":"泽运楼","cityCode":"0668","newCityCode":"0668","adCode":"440983","adName":"信宜市","cityName":"茂名市","bizArea":"东镇街道"},"startDistance":"11247","price":17750,"tipPrice":0,"avatarIndex":105,"orderStatus":10,"distance":352400,"driverMileagePrice":17750,"ruleUrl":"https://m2.hellobike.com/AppHitchMainH5/latest/index.html#/valuation-rule?cityCode=0898&startAdCode=460105&endCityCode=0668&endAdCode=440983&crossCity=1&planStartTime=1724749200000&refineIds=2780947619272720798,2780947619272720764,2795808996397762689,2780947619272720747,2780947619272720730,2795808996397762621,2795808996397762655,2795808996397762638&priceNo=PP2024082681696000000011925310797","driverProPoolFarePrice":17750,"bounty":0,"driverDiscount":0,"totalBounty":0,"startPlanStartTime":1724749200000,"endPlanStartTime":1724750400000,"orderTimes":71,"tollRule":"*跨城订单优先高速路线，高速费等相关费用由车主承担","prePayPriority":false,"passengerProPayPrice":19080,"goodsInfo":{"driverDispatchingFee":0},"bearHighwayFeeType":3,"highwayOrder":true,"driverHighwayFeeViewTag":"愿意协商高速费","instantType":5,"comfortType":0,"comfortExtraFee":18500,"agentType":0,"endPositionName":"东镇街道","endPoiName":"东镇街道","hasPoolFixdPrice":true,"deputyText":"","carPoolText":"拼座","requirements":[[{"text":"乘客愿意协商高速费","color":"#5A6066","fontSize":12}]],"planArriveTime":1724749800000,"departureSoon":false,"valueAddedPrice":0},{"journeyId":"JP2024082650264100002028595332","passengerId":"2028595332","passengerGuid":"d11bf91652ea4773bb0c3444e4753843","passengerName":"尾号5288","journeyType":1,"rating":"5.0","passengerCount":1,"startTime":"1724717400000","isCarPool":1,"startAddr":{"lon":"110.41392077861057","lat":"19.996272919517978","longAddr":"海南省海口市美兰区灵山镇琼山大道266号","shortAddr":"海南林洋混凝土有限公司","cityCode":"0898","newCityCode":"0898","adCode":"460108","adName":"美兰区","cityName":"海口市","bizArea":"灵山镇","poiId":""},"endAddr":{"lon":"109.98474929044","lat":"22.************","longAddr":"玉林市G241（261省道附近）广恒中央城小区","shortAddr":"广恒中央城小区","cityCode":"0775","newCityCode":"0775","adCode":"450923","adName":"博白县","cityName":"玉林市","bizArea":"博白镇","poiId":""},"startDistance":"27438","price":17600,"tipPrice":0,"avatarIndex":104,"orderStatus":10,"distance":348400,"driverMileagePrice":17600,"ruleUrl":"https://m2.hellobike.com/AppHitchMainH5/latest/index.html#/valuation-rule?cityCode=0898&startAdCode=460108&endCityCode=0775&endAdCode=450923&crossCity=1&planStartTime=1724716800000&refineIds=2780947619272720798,2780947619272720764,2795808996397762689,2780947619272720747,2780947619272720730,2795808996397762621,2795808996397762655,2795808996397762638&priceNo=PP2024082620646300000012028595332","driverProPoolFarePrice":17600,"bounty":0,"driverDiscount":0,"totalBounty":0,"startPlanStartTime":1724716800000,"endPlanStartTime":1724718000000,"orderTimes":0,"tollRule":"*跨城订单优先高速路线，高速费等相关费用由车主承担","prePayPriority":false,"passengerProPayPrice":18120,"goodsInfo":{"driverDispatchingFee":0},"bearHighwayFeeType":2,"highwayOrder":true,"driverHighwayFeeViewTag":"不承担高速费","instantType":5,"comfortType":0,"comfortExtraFee":18270,"agentType":0,"endPositionName":"博白镇","endPoiName":"博白镇","hasPoolFixdPrice":true,"deputyText":"","carPoolText":"拼座","requirements":[[{"text":"乘客不承担高速费","color":"#5A6066","fontSize":12}]],"planArriveTime":1724717400000,"departureSoon":false,"valueAddedPrice":0},{"journeyId":"JP2024082650810500001847192722","passengerId":"1847192722","passengerGuid":"e282c0187db04f4c99ecf5ad50cc91e1","passengerName":"尾号5535","journeyType":1,"rating":"5.0","passengerCount":1,"startTime":"1724715300000","isCarPool":1,"startAddr":{"lon":"110.34155","lat":"19.964163","longAddr":"海南省海口市龙华区城西镇海南嘉七文化传媒有限公司金岛国际","shortAddr":"海南嘉七文化传媒有限公司","cityCode":"0898","newCityCode":"0898","adCode":"460106","adName":"龙华区","cityName":"海口市","bizArea":"城西镇","poiId":""},"endAddr":{"lon":"108.366793","lat":"21.679489","longAddr":"防城港市西湾大道龙正海洋公园75号商铺伊香园新疆味道(龙正中央海洋公园店)","shortAddr":"伊香园新疆味道(龙正中央海洋公园店)","cityCode":"0770","newCityCode":"0770","adCode":"450602","adName":"港口区","cityName":"防城港市","bizArea":"沙潭江街道","poiId":"B0FFKGLDZT"},"startDistance":"22049","price":20560,"tipPrice":0,"avatarIndex":103,"orderStatus":10,"distance":432500,"driverMileagePrice":20560,"ruleUrl":"https://m2.hellobike.com/AppHitchMainH5/latest/index.html#/valuation-rule?cityCode=0898&startAdCode=460106&endCityCode=0770&endAdCode=450602&crossCity=1&planStartTime=1724714700000&refineIds=2780947619272720798,2780947619272720764,2795808996397762689,2780947619272720747,2780947619272720730,2795808996397762621,2795808996397762655,2795808996397762638&priceNo=PP2024082631762500000011847192722","driverProPoolFarePrice":20560,"bounty":0,"driverDiscount":0,"totalBounty":0,"startPlanStartTime":1724714700000,"endPlanStartTime":1724715900000,"orderTimes":20,"tollRule":"*跨城订单优先高速路线，高速费等相关费用由车主承担","prePayPriority":false,"passengerProPayPrice":21280,"goodsInfo":{"driverDispatchingFee":0},"bearHighwayFeeType":2,"highwayOrder":true,"driverHighwayFeeViewTag":"不承担高速费","instantType":5,"comfortType":0,"comfortExtraFee":23380,"agentType":0,"endPositionName":"沙潭江街道","endPoiName":"沙潭江街道","hasPoolFixdPrice":true,"deputyText":"","carPoolText":"拼座","requirements":[[{"text":"乘客不承担高速费","color":"#5A6066","fontSize":12}]],"planArriveTime":1724715300000,"departureSoon":false,"valueAddedPrice":0},{"journeyId":"JP2024082655700300001502972365","passengerId":"1502972365","passengerGuid":"978cca63beea4a9884be08085cb3ff9d","passengerName":"尾号2098","journeyType":1,"rating":"5.0","passengerCount":2,"startTime":"1724724300000","isCarPool":1,"startAddr":{"lon":"110.02192","lat":"20.341817","longAddr":"广东省湛江市徐闻县迈陈镇青桐新村","shortAddr":"青桐新村","cityCode":"0759","newCityCode":"0759","adCode":"440825","adName":"徐闻县","cityName":"湛江市","bizArea":"迈陈镇","poiId":""},"endAddr":{"lon":"113.05564371084","lat":"23.075757214058","longAddr":"佛山市依云华府东门161号菜鸟驿站(佛山依云华府店)","shortAddr":"菜鸟驿站(佛山依云华府店)","cityCode":"0757","newCityCode":"0757","adCode":"440605","adName":"南海区","cityName":"佛山市","bizArea":"狮山镇","poiId":""},"startDistance":"59391","price":45350,"tipPrice":0,"avatarIndex":101,"orderStatus":10,"distance":529300,"driverMileagePrice":45350,"ruleUrl":"https://m2.hellobike.com/AppHitchMainH5/latest/index.html#/valuation-rule?cityCode=0759&startAdCode=440825&endCityCode=0757&endAdCode=440605&crossCity=1&planStartTime=1724723700000&refineIds=2739765943813545552,2780947619272744156,2780947619272744139,2739765943813545556,2780947619272744122,2795808996397786013,2739765943813545560,2795808996397786047,2795808996397786030&priceNo=PP2024082644542000000011502972365","driverProPoolFarePrice":45350,"bounty":0,"driverDiscount":0,"totalBounty":0,"startPlanStartTime":1724723700000,"endPlanStartTime":1724724900000,"orderTimes":50,"tollRule":"*跨城订单优先高速路线，高速费等相关费用由车主承担","prePayPriority":false,"passengerProPayPrice":48360,"goodsInfo":{"driverDispatchingFee":0},"bearHighwayFeeType":2,"highwayOrder":true,"driverHighwayFeeViewTag":"不承担高速费","instantType":5,"comfortType":0,"comfortExtraFee":36730,"agentType":0,"endPositionName":"狮山镇","endPoiName":"狮山镇","hasPoolFixdPrice":true,"deputyText":"","carPoolText":"拼座","requirements":[[{"text":"乘客不承担高速费","color":"#5A6066","fontSize":12}]],"planArriveTime":1724724300000,"departureSoon":false,"valueAddedPrice":0},{"journeyId":"JP2024082656750100001771392593","passengerId":"1771392593","passengerGuid":"3a1dbfc048b64bac91a7e5a1654c56de","passengerName":"尾号8403","journeyType":1,"rating":"5.0","passengerCount":1,"startTime":"1724735400000","isCarPool":1,"startAddr":{"lon":"110.35027","lat":"20.032477","longAddr":"海口市海府路15号","shortAddr":"温德森酒店(东湖骑楼老街店)","cityCode":"0898","newCityCode":"0898","adCode":"460108","adName":"美兰区","cityName":"海口市","bizArea":"海府","poiId":""},"endAddr":{"lon":"108.597696","lat":"21.902585","longAddr":"钦州市兰海高速","shortAddr":"好润水产技术服务中心(广西旗舰店)","cityCode":"0777","newCityCode":"0777","adCode":"450702","adName":"钦南区","cityName":"钦州市","bizArea":"尖山街道","poiId":""},"startDistance":"18045","price":18670,"tipPrice":0,"avatarIndex":101,"orderStatus":10,"distance":378800,"driverMileagePrice":18670,"ruleUrl":"https://m2.hellobike.com/AppHitchMainH5/latest/index.html#/valuation-rule?cityCode=0898&startAdCode=460108&endCityCode=0777&endAdCode=450702&crossCity=1&planStartTime=1724734800000&refineIds=2780947619272720798,2780947619272720764,2795808996397762689,2780947619272720747,2780947619272720730,2795808996397762621,2795808996397762655,2795808996397762638&priceNo=PP2024082650014600000011771392593","driverProPoolFarePrice":18670,"bounty":0,"driverDiscount":0,"totalBounty":0,"startPlanStartTime":1724734800000,"endPlanStartTime":1724736000000,"orderTimes":33,"tollRule":"*跨城订单优先高速路线，高速费等相关费用由车主承担","prePayPriority":false,"passengerProPayPrice":20130,"goodsInfo":{"driverDispatchingFee":0},"hasThroughHighway":false,"bearHighwayFeeType":2,"highwayOrder":true,"driverHighwayFeeViewTag":"不承担高速费","instantType":5,"comfortType":0,"comfortExtraFee":20110,"agentType":0,"endPositionName":"尖山街道","endPoiName":"尖山街道","hasPoolFixdPrice":true,"deputyText":"","carPoolText":"拼座","requirements":[[{"text":"乘客不承担高速费","color":"#5A6066","fontSize":12}]],"planArriveTime":1724735400000,"departureSoon":false,"valueAddedPrice":0},{"journeyId":"JP2024082656387500002112436982","passengerId":"2112436982","passengerGuid":"ccefd8b769e94d1c80d3dd1e9f29e81e","passengerName":"尾号1568","journeyType":1,"rating":"5.0","passengerCount":1,"startTime":"1724726400000","isCarPool":1,"startAddr":{"lon":"110.163555","lat":"20.027488","longAddr":"海口站-进站口","shortAddr":"海口站-进站口","cityCode":"0898","newCityCode":"0898","adCode":"460100","adName":"海口市","cityName":"海口市","bizArea":"西秀镇","poiId":""},"endAddr":{"lon":"109.3900378467","lat":"24.************","longAddr":"柳州市广西壮族自治区柳州市柳南区南站路6号","shortAddr":"柳州站-东出站口","cityCode":"0772","newCityCode":"0772","adCode":"450204","adName":"柳南区","cityName":"柳州市","bizArea":"南站街道","poiId":""},"startDistance":"5889","price":26350,"tipPrice":0,"avatarIndex":107,"orderStatus":10,"distance":589200,"driverMileagePrice":26350,"ruleUrl":"https://m2.hellobike.com/AppHitchMainH5/latest/index.html#/valuation-rule?cityCode=0898&startAdCode=460100&endCityCode=0772&endAdCode=450204&crossCity=1&planStartTime=1724725800000&refineIds=2780947619272720798,2780947619272720764,2780947619272720747,2795808996397762689,2780947619272720730,2795808996397762621,2795808996397762655,2795808996397762638&priceNo=PP2024082657061900000012112436982","driverProPoolFarePrice":26350,"bounty":0,"driverDiscount":0,"totalBounty":0,"startPlanStartTime":1724725800000,"endPlanStartTime":1724727000000,"orderTimes":9,"tollRule":"*跨城订单优先高速路线，高速费等相关费用由车主承担","prePayPriority":false,"passengerProPayPrice":28550,"goodsInfo":{"driverDispatchingFee":0},"hasThroughHighway":true,"bearHighwayFeeType":2,"highwayOrder":true,"driverHighwayFeeViewTag":"不承担高速费","instantType":5,"comfortType":0,"comfortExtraFee":32630,"agentType":0,"endPositionName":"南站街道","endPoiName":"南站街道","hasPoolFixdPrice":true,"deputyText":"","carPoolText":"拼座","requirements":[[{"text":"乘客不承担高速费","color":"#5A6066","fontSize":12}]],"planArriveTime":1724726400000,"departureSoon":false,"valueAddedPrice":0},{"journeyId":"JP2024082649130900001598897196","passengerId":"1598897196","passengerGuid":"1d71e1d2aa024e5c88e7321a7c6fbd65","passengerName":"尾号6855","journeyType":1,"rating":"5.0","passengerCount":1,"startTime":"1724724600000","isCarPool":1,"startAddr":{"lon":"110.145673","lat":"20.471219","longAddr":"广东省湛江市徐闻县下桥镇","shortAddr":"文化中心西北侧","cityCode":"0759","newCityCode":"0759","adCode":"440825","adName":"徐闻县","cityName":"湛江市","bizArea":"下桥镇"},"endAddr":{"lon":"109.875621","lat":"21.851167","longAddr":"玉林市博白县","shortAddr":"石板江","cityCode":"0775","newCityCode":"0775","adCode":"450923","adName":"博白县","cityName":"玉林市","bizArea":"那卜镇","poiId":""},"startDistance":"65711","price":11330,"tipPrice":0,"avatarIndex":103,"orderStatus":10,"distance":213000,"driverMileagePrice":11330,"ruleUrl":"https://m2.hellobike.com/AppHitchMainH5/latest/index.html#/valuation-rule?cityCode=0759&startAdCode=440825&endCityCode=0775&endAdCode=450923&crossCity=1&planStartTime=1724724000000&refineIds=2739765943813545552,2780947619272744156,2780947619272744139,2739765943813545556,2780947619272744122,2795808996397786013,2739765943813545560,2795808996397786047,2795808996397786030&priceNo=PP2024082618657600000011598897196","driverProPoolFarePrice":11330,"bounty":0,"driverDiscount":0,"totalBounty":0,"startPlanStartTime":1724724000000,"endPlanStartTime":1724725200000,"orderTimes":80,"tollRule":"*跨城订单优先高速路线，高速费等相关费用由车主承担","prePayPriority":false,"passengerProPayPrice":12560,"goodsInfo":{"driverDispatchingFee":0},"hasThroughHighway":false,"bearHighwayFeeType":3,"highwayOrder":true,"driverHighwayFeeViewTag":"愿意协商高速费","instantType":5,"comfortType":0,"comfortExtraFee":10940,"agentType":0,"endPositionName":"那卜镇","endPoiName":"那卜镇","hasPoolFixdPrice":true,"deputyText":"","carPoolText":"拼座","requirements":[[{"text":"乘客愿意协商高速费","color":"#5A6066","fontSize":12}]],"planArriveTime":1724724600000,"departureSoon":false,"valueAddedPrice":0}],"freLineIndex":-1,"totalCount":20,"recommendMoreIndex":-1,"recommendMark":false,"deliverSwitch":true,"seatCount":5}}`
	var rsp dao.HaluoRspList
	_ = json.Unmarshal([]byte(rspStr), &rsp)

	curlInfosB := GetCurlInfoB(&req)
	type fields struct {
		Ws     *websocket.Handler
		Req    *dao.GradReq
		HadGet map[string]struct{}
		BadTK  map[string]string
	}
	type args struct {
		curlInfo *CurlInfo
		data     *dao.HaluoRspListData
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   int
	}{
		{
			name: "1",
			fields: fields{
				Ws:     nil,
				Req:    &req,
				HadGet: make(map[string]struct{}),
				BadTK:  make(map[string]string),
			},
			args: args{
				curlInfo: curlInfosB[0],
				data:     rsp.Data,
			},
			want: 1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &GradHandler{
				Ws:     tt.fields.Ws,
				Req:    tt.fields.Req,
				HadGet: tt.fields.HadGet,
				BadTK:  tt.fields.BadTK,
			}
			if got := h.GetValidOrder(tt.args.curlInfo, tt.args.data); len(got) != tt.want {
				t.Errorf("GetValidOrder() = %d, want %d", len(got), tt.want)
			}
		})
	}
}
