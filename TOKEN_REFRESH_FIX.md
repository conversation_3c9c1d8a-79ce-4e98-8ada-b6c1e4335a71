# Token自动刷新功能修复

## 问题描述

原项目存在哈啰出行token有效期只有1小时左右的问题，当token失效时系统会停止抢单，需要用户手动重新登录获取新token。

## 解决方案

实现了token自动刷新机制，当检测到token失效时，系统会自动尝试刷新token并继续抢单，无需用户手动干预。

## 修复内容

### 1. 服务端修改 (shenhouS)

#### 1.1 grad.go 修改
- 添加了全局token缓存机制
- 修改了token失效处理逻辑，从直接标记失效改为尝试自动刷新
- 新增 `RefreshToken()` 方法处理token刷新流程
- 新增 `SetNewToken()` 和 `ClearTokenCache()` 方法管理token缓存
- 新增 `checkNewToken()` 和 `updateToken()` 方法处理token更新

#### 1.2 websocket/ws.go 修改
- 添加了 `SendMessage()` 方法支持发送JSON格式消息

#### 1.3 dao/grad_data.go 修改
- 新增 `WsDataTypeTokenRefresh` 常量定义token刷新消息类型
- 新增 `TokenRefreshRequest` 和 `TokenRefreshResponse` 数据结构

#### 1.4 control/halou.go 修改
- 新增 `handleWebSocketMessages()` 方法处理WebSocket消息
- 新增 `handleClientMessage()` 方法解析客户端消息
- 新增 `handleTokenRefreshResponse()` 方法处理token刷新响应

### 2. 客户端修改 (shenhouC)

#### 2.1 main.js 修改
- 在 `WsOnMsg()` 中添加了case 30处理token刷新请求
- 新增 `handleTokenRefreshRequest()` 方法处理服务端的token刷新请求
- 新增 `sendTokenRefreshResponse()` 方法发送token刷新响应
- 实现了智能token更新逻辑，优先使用最新获取的token

## 工作流程

### 自动刷新流程

1. **检测token失效**: 当服务端收到哈啰API返回Code=103时，识别为token失效
2. **发起刷新请求**: 服务端通过WebSocket向客户端发送token刷新请求
3. **客户端处理**: 客户端收到请求后，检查是否有最新可用的token
4. **token更新**: 如果有可用token，客户端更新本地存储并发送响应给服务端
5. **服务端更新**: 服务端收到响应后更新内存中的token并继续抢单
6. **继续抢单**: token更新完成后，系统自动继续抢单流程

### 消息格式

#### 服务端 -> 客户端 (token刷新请求)
```json
{
  "type": 30,
  "body": {
    "phone": "手机号",
    "token_text": "接单TK"
  }
}
```

#### 客户端 -> 服务端 (token刷新响应)
```json
{
  "type": "token_refresh_response",
  "data": {
    "success": true,
    "old_token": "旧token",
    "new_token": "新token", 
    "message": "刷新成功"
  }
}
```

## 使用说明

### 自动刷新条件
1. 用户已经登录并获取了有效token
2. 在"临时Token"输入框中有最新的token
3. WebSocket连接正常

### 手动刷新步骤
如果自动刷新失败，用户需要：
1. 重新获取验证码并登录
2. 点击"保存抢单Token"或"保存刷单Token"更新token
3. 系统会自动使用新token继续抢单

## 技术特点

1. **无缝切换**: token刷新过程中不会中断抢单流程
2. **智能检测**: 自动识别token失效并触发刷新
3. **容错处理**: 刷新失败时提供明确的错误提示
4. **实时通信**: 通过WebSocket实现服务端和客户端的实时通信
5. **线程安全**: 使用互斥锁保护token缓存的并发访问

## 注意事项

1. **验证码限制**: 由于哈啰出行登录需要验证码，完全自动化刷新有限制
2. **token有效期**: 建议用户定期手动刷新token以确保系统稳定运行
3. **网络连接**: 确保WebSocket连接稳定，否则可能影响token刷新功能
4. **多token管理**: 系统支持同时管理多个刷新token的自动更新

## 测试建议

1. 模拟token失效场景测试自动刷新功能
2. 测试WebSocket断线重连后的token刷新
3. 验证多个token同时失效时的处理逻辑
4. 测试刷新失败时的错误处理和用户提示

通过这些修改，系统现在能够自动处理token失效问题，大大提高了用户体验和系统的稳定性。
