package utils

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/dop251/goja"
	"github.com/google/uuid"
	"math/rand"
	"net/url"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"
)

var letters = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")

func IsDebug() bool {
	return os.Getenv("DEBUG") == "1"
}

func RandStr(n int) string {
	b := make([]rune, n)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

func RandStrDown(n int) string {
	l := []rune("abcdefghijklmnopqrstuvwxyz0123456789")
	b := make([]rune, n)
	for i := range b {
		b[i] = l[rand.Intn(len(l))]
	}
	return string(b)
}

func GetGuid() string {
	uuidObj := uuid.New()
	return uuidObj.String()
}
func HmacSHA1(message, key string) string {
	h := hmac.New(sha1.New, []byte(key))
	h.Write([]byte(message))
	return hex.EncodeToString(h.Sum(nil))
}

func GetMd5Guid() string {
	return fmt.Sprintf("%x", md5.Sum([]byte(GetGuid())))
}

func ParamEncode(v any) string {
	dataByte, _ := json.Marshal(v)
	dataParam := "q=" + url.QueryEscape(string(dataByte))
	return dataParam
}

func ExtractNumber(temp interface{}) string {
	text, _ := temp.(string)
	re := regexp.MustCompile(`[-+]?[0-9]*\.?[0-9]+`)
	match := re.FindString(text)
	if match == "" {
		return "0"
	}
	return match
}

func ToInt(match string) int {
	num, err := strconv.ParseFloat(match, 64)
	if err != nil {
		return 0
	}
	return int(num)
}
func ToFloat32(match string) float32 {
	num, err := strconv.ParseFloat(match, 64)
	if err != nil {
		return 0
	}
	return float32(num)
}

func ToFloat64(match string) float64 {
	num, err := strconv.ParseFloat(match, 64)
	if err != nil {
		return 0
	}
	return num
}

func CharContains(str, char string) bool {
	return strings.Contains(str, char)
}

func ExtractDate(input string) (string, int, int, int, int) {
	re := regexp.MustCompile(`(\S+?){(\d+):(\d+)-(\d+):(\d+)}`)
	match := re.FindStringSubmatch(input)
	if match == nil {
		return ExtractDate2(input)
	}
	sH, _ := strconv.Atoi(match[2])
	sM, _ := strconv.Atoi(match[3])
	eH, _ := strconv.Atoi(match[4])
	eM, _ := strconv.Atoi(match[5])
	return match[1], sH, sM, eH, eM
}

func ExtractDate2(input string) (string, int, int, int, int) {
	re := regexp.MustCompile(`(\S+?){(\d+):(\d+)}`)
	match := re.FindStringSubmatch(input)
	if match == nil {
		return "", 0, 59, 23, 59
	}
	sH, _ := strconv.Atoi(match[2])
	sM, _ := strconv.Atoi(match[3])
	return match[1], sH, sM, sH, sM
}

func Eval(funcDef, funcName, args string) (string, error) {
	vm := goja.New()
	_, err := vm.RunString(funcDef)
	if err != nil {
		return "", errors.New(err.Error() + ": 执行func_def失败")
	}
	script := `var res = %s(%s)`
	script = fmt.Sprintf(script, funcName, args)
	_, err = vm.RunString(script)
	if err != nil {
		return "", errors.New(err.Error() + ": 执行加密错误")
	}
	return vm.Get("res").String(), nil
}

func UrlParam(params map[string]string) string {
	var encodedParams []string
	for key, value := range params {
		encodedKey := url.QueryEscape(key)
		encodedValue := url.QueryEscape(value)
		encodedParams = append(encodedParams, encodedKey+"="+encodedValue)
	}
	return strings.Join(encodedParams, "&")
}

func PrintTime(currentTime time.Time) string {
	if currentTime.IsZero() {
		currentTime = time.Now()
	}
	formattedTime := currentTime.Format("2006-01-02 15:04:05.999")
	return formattedTime
}

func GetFresh(min, max int) int {
	source := rand.NewSource(time.Now().UnixNano())
	rng := rand.New(source)
	return rng.Intn(max-min+1) + min
}

func SplitToken(t string) []string {
	temp := strings.Split(t, "\n")
	list := make([]string, 0, len(temp)/2)
	for _, s := range temp {
		tempS := strings.Trim(s, " ")
		tempS = strings.Trim(tempS, "\r")
		tempS = strings.Trim(tempS, "\n")
		if len(tempS) > 0 {
			list = append(list, tempS)
		}
	}
	return list
}

func GetDateDescription(timestamp int64) string {
	// 将时间戳转换为时间
	t := time.Unix(timestamp, 0)

	curDate := t.Format(time.DateOnly)
	now := time.Now()

	// 比较日期
	switch curDate {
	case now.Format(time.DateOnly):
		return "今天"
	case now.AddDate(0, 0, 1).Format(time.DateOnly):
		return "明天"
	case now.AddDate(0, 0, 2).Format(time.DateOnly):
		return "后天"
	default:
		return t.Format("01-02") // 格式化为具体日期
	}
}

func ShuffleStrList(arr []string) {
	for i := len(arr) - 1; i > 0; i-- {
		j := rand.Intn(i + 1)
		arr[i], arr[j] = arr[j], arr[i]
	}
}

func TrimArrayStr(list []string) []string {
	newList := make([]string, 0, len(list))
	for _, s := range list {
		s = strings.Trim(s, " ")
		if len(s) > 0 {
			newList = append(newList, s)
		}
	}
	return newList
}
func GetHost(urlStr string) (string, error) {
	// 解析URL
	u, err := url.Parse(urlStr)
	if err != nil {
		return "", fmt.Errorf("invalid URL: %v", err)
	}

	// 获取主机部分
	host := u.Host
	if host == "" {
		return "", fmt.Errorf("URL does not contain a host")
	}

	// 如果主机包含端口号，我们只返回主机名
	if portIndex := u.Port(); portIndex != "" {
		host = host[:len(host)-len(portIndex)-1] // 去除冒号和端口号
	}

	return host, nil
}
