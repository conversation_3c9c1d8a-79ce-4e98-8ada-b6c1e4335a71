package dao

type HaluoRspBase struct {
	Code int                    `json:"code"`
	Msg  string                 `json:"msg"`
	Data map[string]interface{} `json:"data"`
}

type HaluoRspErr struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data string `json:"data"`
}

type HaluoRspList struct {
	Code int               `json:"code"`
	Msg  string            `json:"msg,omitempty"`
	Data *HaluoRspListData `json:"data,omitempty"`
}

type HaluoRspListData struct {
	StartAddr       *HaluoRspAddr   `json:"startAddr"`
	EndAddr         *HaluoRspAddr   `json:"endAddr"`
	DriverJourneyId string          `json:"driverJourneyId"`
	List            []*HaluoRspItem `json:"list"`
}

type HaluoRspAddr struct {
	AdCode    string `json:"adCode"`
	AdName    string `json:"adName"`
	CityCode  string `json:"cityCode"`
	CityName  string `json:"cityName"`
	LongAddr  string `json:"longAddr"`
	ShortAddr string `json:"shortAddr"`
	BizArea   string `json:"bizArea"`
	Lat       string `json:"lat"`
	Lon       string `json:"lon"`
	PoiId     string `json:"poiId"`
	PoiName   string `json:"poi"`
	Type      string `json:"type"`
}

type HaluoRspSimuCallPriceInfo struct {
	ProductCode int    `json:"productCode"`
	ActualPrice int    `json:"actualPrice"`
	CarPoolText string `json:"carPoolText"`
}

type HaluoRspItem struct {
	Id                 string                       `json:"journeyId"`
	PassengerCount     int                          `json:"passengerCount"`
	IsCarPool          int                          `json:"isCarPool"`
	HitchPercent       float64                      `json:"hitchPercent"`
	StartDistance      string                       `json:"startDistance"`
	EndDistance        string                       `json:"endDistance"`
	Price              int                          `json:"price"`
	Distance           int                          `json:"distance"`
	StartPlanStartTime int64                        `json:"startPlanStartTime"`
	EndPlanStartTime   int64                        `json:"endPlanStartTime"`
	CarPoolText        string                       `json:"carPoolText"`
	StartAddr          *HaluoRspAddr                `json:"startAddr"`
	EndAddr            *HaluoRspAddr                `json:"endAddr"`
	SimuCallPriceInfo  []*HaluoRspSimuCallPriceInfo `json:"simuCallPriceInfo"`
}

type HaluoJourneyRsp struct {
	Code int                    `json:"code"`
	Data []*HaluoJourneyRspItem `json:"data"`
	Msg  string                 `json:"msg"`
}

type HaluoJourneyRspItem struct {
	JourneyGuid   string        `json:"driverJourneyGuid"`
	OrderStatus   int           `json:"orderStatus"`
	PlanStartTime int64         `json:"planStartTime"`
	StartPosition *HaluoRspAddr `json:"startPosition"`
	EndPosition   *HaluoRspAddr `json:"endPosition"`
}

type HaluoReceiveRsp struct {
	Code int                    `json:"code"`
	Msg  string                 `json:"msg"`
	Data map[string]interface{} `json:"data"`
}

type HaluoPublishRsp struct {
	Code int                  `json:"code"`
	Data *HaluoPublishRspData `json:"data"`
	Msg  string               `json:"msg"`
}

type HaluoPublishRspData struct {
	DriverJourneyGuid string `json:"driverJourneyGuid"`
}

type HaluoRspSecretKey struct {
	Code int             `json:"code"`
	Msg  string          `json:"msg"`
	Data *HaluoSecretKey `json:"data"`
}

type HaluoRspHelloToken struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		HelloToken  string `json:"helloToken"`
		RetryConfig []int  `json:"retryConfig"`
		DownGrade   bool   `json:"downGrade"`
		SilentTime  int    `json:"silentTime"`
	} `json:"data"`
}

type HaluoCheckListRsp struct {
	Code int               `json:"code"`
	Msg  string            `json:"msg"`
	Data []*HaluoCheckItem `json:"data"`
}

type HaluoCheckItem struct {
	DriverJourneyGuid string               `json:"driverJourneyGuid"`
	OrderStatus       int                  `json:"orderStatus"`
	PaxOrderList      []*HaluoPaxOrderItem `json:"paxOrderList"`
}

type HaluoPaxOrderItem struct {
	DriverJourneyGuid    string `json:"driverJourneyGuid"`
	PassengerJourneyGuid string `json:"passengerJourneyGuid"`
	OrderStatus          int    `json:"orderStatus"`
}

type HaluoAutoReceiveRuleRsp struct {
	Code int                       `json:"code"`
	Msg  string                    `json:"msg"`
	Data *HaluoAutoReceiveRuleData `json:"data"`
}

type HaluoAutoReceiveRuleData struct {
	PageId         string                             `json:"pageId"`
	FunctionParams HaluoAutoReceiveRuleFunctionParams `json:"functionParams"`
}

type HaluoAutoReceiveRuleFunctionParams struct {
	State HaluoAutoReceiveRuleState `json:"state"`
}

type HaluoAutoReceiveRuleState struct {
	RowStates []HaluoAutoReceiveRuleRowState `json:"rowStates"`
}

type HaluoAutoReceiveRuleRowState struct {
	RowId        int                            `json:"rowId"`
	MinStepValue int                            `json:"minStepValue"` // 1 2 3
	FlowItems    []HaluoAutoReceiveRuleFlowItem `json:"flowItems"`    // 4 5 9
}

type HaluoAutoReceiveRuleFlowItem struct {
	Id        int   `json:"id"`
	StartTime int64 `json:"startTime"`
	EndTime   int64 `json:"endTime"`
}

type HaluoCapRsp struct {
	// {"code":0,"msg":"ok","data":{"success":1,"captchaId":"fd462d7fa7f47be21fc2b675f741aa37","riskType":"","challenge":"1173185873079341056","ex":1800}}
	Code int           `json:"code"`
	Msg  string        `json:"msg"`
	Data *HaluoCapData `json:"data"`
}

type HaluoCapData struct {
	Success   int    `json:"success"`
	CaptchaId string `json:"captchaId"`
	RiskType  string `json:"riskType"`
	Challenge string `json:"challenge"`
	Ex        int    `json:"ex"`
}

type CapResult struct {
	Status string `json:"status"`
	Data   struct {
		LotNumber string `json:"lot_number"`
		Result    string `json:"result"`
		FailCount int    `json:"fail_count"`
		Seccode   struct {
			CaptchaId     string `json:"captcha_id"`
			LotNumber     string `json:"lot_number"`
			PassToken     string `json:"pass_token"`
			GenTime       string `json:"gen_time"`
			CaptchaOutput string `json:"captcha_output"`
		} `json:"seccode"`
		Score           string `json:"score"`
		Payload         string `json:"payload"`
		ProcessToken    string `json:"process_token"`
		PayloadProtocol int    `json:"payload_protocol"`
	} `json:"data"`
}

type HaluoCheckCapRsp struct {
	//{"code":0,"msg":"ok","data":{"success":0,"msg":"Please verify"}}
	Code int    `json:"code"`
	Msg  string `json:"msg"`

	Data struct {
		Success int    `json:"success"`
		Msg     string `json:"msg"`
	} `json:"data"`
}
