package gaode

type Geocode struct {
	AdCode string `json:"adcode"`
	//Building         GeoRspExt `json:"building"`
	City             string `json:"city"`
	CityCode         string `json:"citycode"`
	Country          string `json:"country"`
	District         string `json:"district"`
	FormattedAddress string `json:"formatted_address"`
	Level            string `json:"level"`
	Location         string `json:"location"`
	//Neighborhood     GeoRspExt `json:"neighborhood"`
	//Number           []string  `json:"number"`
	//Province         string    `json:"province"`
	//Township         []string  `json:"township"`
	//Street           []string  `json:"street"`
}

type GeoRspExt struct {
	Name []string `json:"name"`
	Type []string `json:"type"`
}

type GeoRsp struct {
	Status   string    `json:"status"`
	Info     string    `json:"info"`
	InfoCode string    `json:"infocode"`
	Count    string    `json:"count"`
	Geocodes []Geocode `json:"geocodes"`
}
