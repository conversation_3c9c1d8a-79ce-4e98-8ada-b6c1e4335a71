package main

import (
	"embed"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/limitzhang87/shenhouS/internal/control"
	"github.com/limitzhang87/shenhouS/internal/dao"
	"github.com/limitzhang87/shenhouS/internal/module/city"
	"github.com/limitzhang87/shenhouS/internal/utils"
	"io"
	"os"
)

//go:embed public/adcode_citycode.csv
var DataCSV embed.FS

//go:embed public/haluo_aes.js
var DataJs embed.FS

func init() {
	city.InitCityCodeMap(DataCSV)
	utils.InitJsFile(DataJs)
}

func main() {
	testCsv()
	ginStart()

	//decode()
	//testRec()
}

func ginStart() {
	gin.DefaultErrorWriter = io.MultiWriter(os.Stdout)
	r := gin.Default()
	r.GET("/", control.Check)
	r.GET("/wsStart", control.StartWs)
	err := r.Run(":8081")
	if err != nil {
		fmt.Println(err)
	}
}

func testCsv() {
	cityCode := city.GetCityCode("320411")
	if cityCode != "0519" {
		fmt.Printf("城市码错误")
		os.Exit(0)
	}
}

func decode() {
	data := `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`
	res, _ := utils.AesDecrypt(data)
	fmt.Println(res)

	//res, _ := utils.AesDecryptNew("9e08a6313e0a1112", data)
	//fmt.Println(res)
}

func testRec() {
	data := `{"code":0,"msg":"ok","data":[{"driverJourneyGuid":"JP2024082460032200001747499079","orderStatus":10,"planStartTime":1724475600000,"poolStatus":0,"startPosition":{"longAddr":"常州市01号","shortAddr":"恒记宴会中心(莱蒙店)","cityCode":"0519","cityName":"常州市","adCode":"320412","adName":"武进区"},"endPosition":{"longAddr":"海门市北京中路777号","shortAddr":"南通东恒盛国际大酒店","cityCode":"0513","cityName":"南通市","adCode":"320614","adName":"海门区"},"hasMoreMatchOrder":false,"journeyType":1,"paxOrderList":[],"sortDateTime":1724475600000,"publishOrderType":0,"validPackageOrder":false},{"driverJourneyGuid":"JP2024082460350900001747499079","orderStatus":20,"planStartTime":1724493600000,"poolStatus":0,"startPosition":{"longAddr":"曼可基(卢家巷店)","shortAddr":"曼可基(卢家巷店)","cityCode":"0519","cityName":"常州市","adCode":"320412","adName":"武进区"},"endPosition":{"longAddr":"连云港市小伊镇中大街128号","shortAddr":"枫度造型","cityCode":"0518","cityName":"连云港市","adCode":"320723","adName":"灌云县"},"hasMoreMatchOrder":false,"journeyType":1,"paxOrderList":[{"driverJourneyGuid":"JP2024082460350900001747499079","passengerJourneyGuid":"JP2024082452325100002279453206","startPosition":{"longAddr":"曼可基(卢家巷店)","shortAddr":"曼可基(卢家巷店)","cityCode":"0519","cityName":"常州市","adCode":"320412","adName":"武进区"},"endPosition":{"longAddr":"连云港市小伊镇中大街128号","shortAddr":"枫度造型","cityCode":"0518","cityName":"连云港市","adCode":"320723","adName":"灌云县"},"orderStatus":30,"passengerStartTime":1724493600000,"planEarlierstTime":1724493600000,"planLatestTime":1724493600000,"mobilePhone":"17804423817","journeyType":1,"passengerId":"2279453206","needPayBlamed":false,"pkSuccessInfo":{"alertDslUrl":"https://resource.51downapp.cn/977221cd17b27166e3aed4ea04ca9a88.json?supportVersion=6.57.0&md5=977221cd17b27166e3aed4ea04ca9a88&ts=20240131-113724&engine=1&scene=pk_result&subScene=pk_result_alert","dslData":{"type":3,"homepageSuccessData":{"lottie":"https://resource.51downapp.cn/<EMAIL>","title":"恭喜你，抢单成功","subtitle":"恭喜你，抢单成功；你的综合评分高于其他车主，本单已接单","succeededOrderConfig":{"labelArray":[{"content":"今天 18:00"},{"content":"1人"},{"content":"拼座"}],"startAddress":"曼可基(卢家巷店)","endAddress":"枫度造型","orderLink":"hellobike://hellobike.com/hitch_enter_detail?passengerOrderId=JP2024082452325100002279453206&driverOrderId=JP2024082460350900001747499079&type=2"},"buttonTitle":"查看抢单成功订单","paxJourneyGuid":"JP2024082452325100002279453206"}}}}],"sortDateTime":1724493600000,"publishOrderType":0,"validPackageOrder":false},{"driverJourneyGuid":"JP2024082460242000001747499079","orderStatus":20,"planStartTime":1724666400000,"poolStatus":0,"startPosition":{"longAddr":"江苏省泰州市泰兴市济川街道龙河路佳源·威尼斯城","shortAddr":"龙河路(路口)西侧","cityCode":"0523","cityName":"泰州市","adCode":"321283","adName":"泰兴市"},"endPosition":{"longAddr":"南京市江苏省南京市鼓楼区凤凰街道行健路阳光广场(龙园中路)","shortAddr":"肯定之星假日旅店(龙江四店)","cityCode":"025","cityName":"南京市","adCode":"320106","adName":"鼓楼区"},"hasMoreMatchOrder":false,"journeyType":1,"paxOrderList":[{"driverJourneyGuid":"JP2024082460242000001747499079","passengerJourneyGuid":"JP2024082439618200001279256636","startPosition":{"longAddr":"江苏省泰州市泰兴市济川街道龙河路佳源·威尼斯城","shortAddr":"龙河路(路口)西侧","cityCode":"0523","cityName":"泰州市","adCode":"321200","adName":"泰州市"},"endPosition":{"longAddr":"南京市江苏省南京市鼓楼区凤凰街道行健路阳光广场(龙园中路)","shortAddr":"肯定之星假日旅店(龙江四店)","cityCode":"025","cityName":"南京市","adCode":"320100","adName":"南京市"},"orderStatus":30,"passengerStartTime":1724666400000,"planEarlierstTime":1724666400000,"planLatestTime":1724667600000,"mobilePhone":"17834756479","journeyType":1,"passengerId":"1279256636","needPayBlamed":false,"pkSuccessInfo":{"alertDslUrl":"https://resource.51downapp.cn/977221cd17b27166e3aed4ea04ca9a88.json?supportVersion=6.57.0&md5=977221cd17b27166e3aed4ea04ca9a88&ts=20240131-113724&engine=1&scene=pk_result&subScene=pk_result_alert","dslData":{"type":3,"homepageSuccessData":{"lottie":"https://resource.51downapp.cn/<EMAIL>","title":"恭喜你，抢单成功","subtitle":"恭喜你，抢单成功；你的综合评分高于其他车主，本单已接单","succeededOrderConfig":{"labelArray":[{"content":"8月26日 18:00"},{"content":"3人"},{"content":"独享"}],"startAddress":"龙河路(路口)西侧","endAddress":"肯定之星假日旅店(龙江四店)","orderLink":"hellobike://hellobike.com/hitch_enter_detail?passengerOrderId=JP2024082439618200001279256636&driverOrderId=JP2024082460242000001747499079&type=2"},"buttonTitle":"查看抢单成功订单","paxJourneyGuid":"JP2024082439618200001279256636"}}}}],"sortDateTime":1724666400000,"publishOrderType":0,"validPackageOrder":false}]}`

	var listRspObj dao.HaluoCheckListRsp
	err := json.Unmarshal([]byte(data), &listRspObj)
	if err != nil {
		fmt.Println(err)
		return
	}
	if listRspObj.Code != 0 {
		fmt.Println(0)
		return
	}
	id := "JP2024082452325100002279453206"
	for _, item := range listRspObj.Data {
		for _, orderItem := range item.PaxOrderList {
			fmt.Println(orderItem.OrderStatus, orderItem.PassengerJourneyGuid)
			if (item.OrderStatus == 20 || orderItem.OrderStatus == 20 || orderItem.OrderStatus == 30) && orderItem.PassengerJourneyGuid == id {
				fmt.Println(1)
				return
			}
		}
	}
	fmt.Println(-1)
}
