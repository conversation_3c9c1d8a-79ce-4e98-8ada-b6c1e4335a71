package city

import (
	"embed"
	"encoding/csv"
	"log"
)

const (
	CsvPath = "public/adcode_citycode.csv"
)

var CodeMap map[string]string

func InitCityCodeMap(fs embed.FS) {
	// 打开CSV文件
	file, err := fs.Open(CsvPath)
	if err != nil {
		log.Fatal(err)
	}
	defer func() {
		_ = file.Close()
	}()

	// 创建CSV读取器
	reader := csv.NewReader(file)

	// 读取CSV文件的所有内容
	records, err := reader.ReadAll()
	if err != nil {
		log.Fatal(err)
	}
	CodeMap = make(map[string]string, 3526)
	// 打印每一行的数据
	for _, record := range records {
		CodeMap[record[1]] = record[2]
	}
}

func GetCityCode(adCode string) string {
	return CodeMap[adCode]
}
