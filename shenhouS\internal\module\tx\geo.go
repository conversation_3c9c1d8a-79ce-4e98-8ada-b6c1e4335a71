package tx

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitzhang87/shenhouS/internal/utils"
	"net/url"
)

const (
	UrlPath = "https://apis.map.qq.com/ws/geocoder/v1/?address=%s&callback=QQmap&key=%s"
)

func Geo(client *utils.HTTPClient, addr string) (map[string]string, error) {
	keys := []string{
		"EM7BZ-UXHW6-RLKSZ-E4LLW-RQITO-R6BNX",
		"A7XBZ-5BGKL-HKVP3-EFFJQ-GQJWT-AJBG2",
		"ONLBZ-G2RKL-DKVPS-ERRLG-CGHW6-AMFCX",
		"MBXBZ-EAIC4-6U2UU-KN3N4-RUILS-FBB3J",
		"HKFBZ-46PCG-KNYQS-QA4FW-XS6ZH-LZF5I",
		"IVYBZ-WL76Q-H3C5M-4ERLI-3YNQO-L6FXT",
		"ZMRBZ-IE56B-GRVU4-N2H2L-7BAUE-XGBDQ",
	}
	utils.ShuffleStrList(keys)
	addr = url.QueryEscape(addr)

	if client == nil {
		client = utils.NewHTTPClient(utils.GetProxyIpHttps())
	}
	var rsp *GeoRsp
	for _, key := range keys {
		// 请求腾讯接口
		baseUrl := fmt.Sprintf(UrlPath, addr, key)
		body, _, err := client.Get(baseUrl, nil)
		if err != nil {
			return nil, errors.New("腾讯获取经纬度异常")
		}

		err = json.Unmarshal([]byte(body), &rsp)
		if err != nil {
			fmt.Println(err)
			return nil, errors.New("腾讯获取经纬度异常1")
		}
		if rsp.Status == 0 {
			break
		}
	}
	if rsp.Status != 0 {
		return nil, errors.New("地址错误")
	}
	return map[string]string{
		"lng":    fmt.Sprintf("%f", rsp.Result.Location.Lng),
		"lat":    fmt.Sprintf("%f", rsp.Result.Location.Lat),
		"adCode": rsp.Result.AdInfo.AdCode,
	}, nil
}
