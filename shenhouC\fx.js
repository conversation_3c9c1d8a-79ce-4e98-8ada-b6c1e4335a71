let FX = {}
http.__okhttp__.setMaxRetries(0);
http.__okhttp__.setTimeout(5*1000);

var TKSEP = "&*&";
FX.GetIp = function() {
    if (IsTest == 1) {
        return "127.0.0.1:8081";
    }

    // let hosts = ["x.viphot.com.cn", "y.viphot.com.cn"];
    let hosts = ["p.loii.top", "q.loii.top"];
    hosts = utils.shuffleArray(hosts);
    for (let host of hosts) {
        try {
            let res = http.get("http://" + host);
            let obj = res.body.string();
            if (obj == '{"code":0}') {
                return host
            }
        } catch (e) {
            log(e)
        }
    }
    return hosts[0];
}


// 发送验证码
FX.SendCode = function(phone) {
    let url = "https://api.hellobike.com/api?user.account.sendCodeV2";
    let header = {
        "host": "api.hellobike.com",
        "sec-fetch-mode": "cors",
        "referer": "https://m.hellobike.com/",
        "sec-fetch-site": "same-site",
        "requestid": "42E5hPmi5PcvyC3",
        "origin": "https://m.hellobike.com",
        "x-requested-with": "mark.via",
        "accept-encoding": "gzip:deflate",
        "accept": "application/json:text/plain:*/*",
        "sec-fetch-dest": "empty",
        "user-agent": "Mozilla/5.0 (Linux; Android 12; DCO-AL00 Build/QKQ1.191222.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/98.0.4758.101 Mobile Safari/537.36",
        "content-type": "application/json; charset=utf-8"
    }
    let param =  {
        "mobile": phone+"",
        "systemCode": 63,
        "channelId": 1,
        "platform": 1,
        "action": "user.account.sendCodeV2",
        "appVersion": "8.44.5",
        "capText": "",
        "from": "h5",
        "version": "1.4.39",
        "adsource": "",
        "adCode": "",
        "cityCode": ""
    }
    let paramStr = JSON.stringify(param);
    try {
        let res = http.request(url, {
            headers: header,
            method: "POST",
            body: paramStr
        });
        let obj = res.body.json();
        if (obj.code != 0) {
            let msg = "哈啰返回错误"
            if (obj.msg != undefined && obj.msg != "undefind") {
                msg = obj.msg
            } else {
                console.error(JSON.stringify(obj))
            }
            toast(msg);
            return 0
        }
    } catch (e) {
        console.log(e);
        utils.toastLog("获取验证码错误");
        return 0
    }
    return 1;
}

FX.Login = function(phone, code) {
    let url = "https://marketingapi.hellobike.com/api?moon.activity.dispatch=";
    let header = {
        "host": "marketingapi.hellobike.com",
        "sec-fetch-mode": "cors",
        "referer": "https://m.hellobike.com/",
        "sec-fetch-site": "same-site",
        "requestid": "42E5hPmi5PcvyC3",
        "origin": "https://m.hellobike.com",
        "x-requested-wit": "mark.via",
        "accept-encoding": "gzip:deflate",
        "accept": "application/json:text/plain:*/*",
        "sec-fetch-dest": "empty",
        "user-agent": "Mozilla/5.0 (Linux; Android 12; DCO-AL00 Build/QKQ1.191222.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/98.0.4758.101 Mobile Safari/537.36",
        "content-type": "application/json; charset=utf-8",
        "content-length": "213",
    }
    let param = {
        "param": {
            "activityCode": "",
            "userMobile": phone + "",
            "verifyCode": code + "",
        },
        "systemCode": 62,
        "channelId": 1,
        "platform": 1,
        "action": "moon.activity.dispatch",
        "method": "register",
        "adsource": "",
        "adCode": "",
        "cityCode": ""
    }
    let res = http.request(url, {
        headers: header,
        method: "POST",
        body: JSON.stringify(param)
    });
    let obj = res.body.json();
    return obj;
}

// 根据地址获取金纬度信息
FX.GetGeo = function(address) {
    let keys = [
        "EM7BZ-UXHW6-RLKSZ-E4LLW-RQITO-R6BNX",
		"A7XBZ-5BGKL-HKVP3-EFFJQ-GQJWT-AJBG2",
		"ONLBZ-G2RKL-DKVPS-ERRLG-CGHW6-AMFCX",
		"MBXBZ-EAIC4-6U2UU-KN3N4-RUILS-FBB3J",
		"HKFBZ-46PCG-KNYQS-QA4FW-XS6ZH-LZF5I",
		"IVYBZ-WL76Q-H3C5M-4ERLI-3YNQO-L6FXT",
		"ZMRBZ-IE56B-GRVU4-N2H2L-7BAUE-XGBDQ",
		"XIHBZ-3PT3J-DDUF3-X4YON-KXPD2-RQFSG",
		"4ROBZ-3NY3Q-7FF5H-43PQ3-L4S7O-62BDE",
		"L7GBZ-FZUWJ-OXXFY-XEM5X-IAED3-2YFRO",
		"MKABZ-IUMCH-4VWDU-WDO7M-NEMLZ-AJBNT",
		"HSQBZ-DANWZ-ACQXY-ZFQHB-5ERKV-RLFGF",
    ];
    keys = utils.shuffleArray(keys);
    let url = "https://apis.map.qq.com/ws/geocoder/v1/?address="+encodeURIComponent(address)+"&callback=QQmap&key=";
    //{"status":121,"message":"此key每日调用量已达到上限","request_id":"6c04d0cf0a524222b3e066659bea0f4d"}
    try {
        let res = "";
        let obj = "";
        for (let key of keys) {
            res = http.get(url + key);
            obj = res.body.json();
            if (obj.status == 0) {
                break
            }
        }
        if (obj.status != 0) {
            console.log(obj.message);
            utils.toastErr("未取到定位，请输入更详细的地址");
            return "";
        }
        return {
            "adCode": obj.result.ad_info.adcode,
            "lat": obj.result.location.lat,
            "lng": obj.result.location.lng,
            "cityName": obj.result.address_components.city
        }
    } catch (e) {
        console.log("请求地址服务异常");
        return ""
    }
}

// 根据地址获取经纬度信息
FX.GetLocation = function(lat, lng) {
    let keys = [
        "EM7BZ-UXHW6-RLKSZ-E4LLW-RQITO-R6BNX",
		"A7XBZ-5BGKL-HKVP3-EFFJQ-GQJWT-AJBG2",
		"ONLBZ-G2RKL-DKVPS-ERRLG-CGHW6-AMFCX",
		"MBXBZ-EAIC4-6U2UU-KN3N4-RUILS-FBB3J",
		"HKFBZ-46PCG-KNYQS-QA4FW-XS6ZH-LZF5I",
		"IVYBZ-WL76Q-H3C5M-4ERLI-3YNQO-L6FXT",
		"ZMRBZ-IE56B-GRVU4-N2H2L-7BAUE-XGBDQ",
		"XIHBZ-3PT3J-DDUF3-X4YON-KXPD2-RQFSG",
		"4ROBZ-3NY3Q-7FF5H-43PQ3-L4S7O-62BDE",
		"L7GBZ-FZUWJ-OXXFY-XEM5X-IAED3-2YFRO",
		"MKABZ-IUMCH-4VWDU-WDO7M-NEMLZ-AJBNT",
		"HSQBZ-DANWZ-ACQXY-ZFQHB-5ERKV-RLFGF",
    ];
    keys = utils.shuffleArray(keys);
    // 31.104084,121.042893
    let location = lat+","+lng
    let url = "https://apis.map.qq.com/ws/geocoder/v1/?location="+location+"&callback=QQmap&key=";
    try {
        let res = "";
        let obj = "";
        for (let key of keys) {
            res = http.get(url + key);
            obj = res.body.json();
            if (obj.status == 0) {
                break
            }
        }
        if (obj.status != 0) {
            console.log(obj.message);
            return "";
        }
        return {
            "adCode": obj.result.ad_info.adcode,
            "address": obj.result.address,
            "lat": obj.result.location.lat,
            "lng": obj.result.location.lng,
            "cityName": "",
        }
    } catch (e) {
        console.log("请求地址服务异常");
        return ""
    }
}

// 获取车辆信息
FX.GetDriver = function(tk) {
    let token = tk.split(TKSEP);
    let url = "https://hitchregistergw.hellobike.com/api?register.person.vehicles";

    let header = {
        "host": "hitchregistergw.hellobike.com",
        "accept": "application/json, text/plain, */*",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090b19)XWEB/9193",
        "content-type": "application/json;charset=UTF-8",
        "origin": "https://m.hellobike.com",
        "sec-fetch-site": "same-site",
        "sec-fetch-mode": "cors",
        "sec-fetch-dest": "empty",
        "referer": "https://m.hellobike.com/",
        // "accept-encoding": "gzip, deflate, br",
        "accept-language": "vzh-CN,zh;q=0.9"
    }
    let param = {"version":"0.1.0","from":"h5","systemCode":"64","platform":63,"riskControlData":{},"action":"register.person.vehicles","bizScene":"201","token":token[1],"ticket":null}
    try {
        let res = http.request(url, {
            headers: header,
            method: "POST",
            body: JSON.stringify(param)
        });
        let obj = res.body.json();
        if (obj.code != 0) {
            utils.toastLog(obj.msg)
            return {"carNum": ""};
        }
        let data = obj.data;
        let carNum = data.allVehicles[0].vehiclePlateNum;
        return {
            "carNum": carNum,
        }
    } catch (e) {
        utils.toastLog("获取司机信息为空")
        return {"carNum": ""};
    }
};

FX.GetDRouteId = function (routeIndex, tk, lat, lng) {
    let ticket = tk.split(TKSEP);
    let url = "https://taxiapi.hellobike.com/api?hitch.core.checkJourneyListV2";
    let param = {
        "systemPlatform": "ios",
        "mobileModel": "",
        "mobileSystem": "iOS 16.6",
        "SDKVersion": "3.4.4",
        "action": "hitch.core.checkJourneyListV2",
        "lon": null,
        "riskControlData": {
            "batteryLevel": "",
            "openId": "",
            "unionId": "",
            "deviceLon": lng,
            "deviceLat": lat,
            "systemCode": "64",
            "network": "wifi"
        },
        "systemCode": "64",
        "version": "6.63.0",
        "ticket": ticket[0],
        "journeyTypeList": [1],
        "adCode": "",
        "appName": "AppHitchDriverApplyWXMP",
        "token": ticket[1],
        "userNewId": null,
        "from": "weChat",
        "cityCode": "",
        "lat": null,
        "releaseVersion": "6.63.0",
        "weChatVersion": "8.0.49"
    }
    let header = {
        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x1800312d) NetType/WIFI Language/zh_CN"
    }
    let res = http.request(url, {
        headers: header,
        method: "POST",
        body: JSON.stringify(param)
    });
    try {
        let obj = res.body.json();

        if (obj.code != 0) {
            utils.toastLog("获取行程路线为空")
            return {};
        }
        let list = obj.data;
        let routeIds = {};
        let newList  = [];
        for (let item of list) {
            if (item.orderStatus == 10 && item.paxOrderList.length <= 0) {
                newList.push(item);
            }
        }

        for (let i of routeIndex) {
            let routeInfo = newList[i-1];
            if (routeInfo == undefined || routeInfo == "undefined") {
                utils.toastLog("未找到行程路线" + i + ", 请检查");
                return {};
            }
            routeIds[i] = {
                "id": routeInfo.driverJourneyGuid+"",
                "adCode": routeInfo.startPosition.adCode+"",
                "cityCode": routeInfo.startPosition.cityCode+"",
                "time": routeInfo.planStartTime,
                "startAddr": routeInfo.startPosition.shortAddr,
                "endAddr": routeInfo.endPosition.shortAddr,
            }
        }
        return routeIds;
    } catch (e) {
        utils.toastLog("获取行程路线为空")
        return {};
    }
}

importPackage(Packages["okhttp3"]); //导入包
FX.StartWs = function(param, wsListener) {
    let client = new OkHttpClient.Builder().retryOnConnectionFailure(true).build();
    let url = "ws://"+FX.GetIp()+"/wsStart?"+param;
    let request = new Request.Builder().url(url).build();
    client.dispatcher().cancelAll();//清理一次
    return client.newWebSocket(request, new WebSocketListener(wsListener)); //创建链接
}
module.exports = FX;