worker_processes auto;

events {
    worker_connections 10240;
}


http {
    log_format upstreaminfo '$remote_addr - $remote_user [$time_local] "$request" '
	                    '$status $body_bytes_sent "$http_referer" '
		            '"$http_user_agent" "$http_x_forwarded_for" '
			    'upstream: $upstream_addr';
    access_log /var/log/nginx/access.log upstreaminfo;
    include mime.types;
    default_type application/octet-stream;
    sendfile off;
    keepalive_timeout 65;
    server_tokens off;
    #access_log off;
    #access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    upstream backendServer {
        least_conn;
	    server r.loii.top:8081 max_fails=3 fail_timeout=30s;
	    server s.loii.top:8081 max_fails=3 fail_timeout=30s;
	    server t.loii.top:8081 max_fails=3 fail_timeout=30s;
	    server u.loii.top:8081 max_fails=3 fail_timeout=30s;
	    server v.loii.top:8081 max_fails=3 fail_timeout=30s;
        keepalive 64;  # 启用连接池，保持64个空闲连接
    }


    server {
        listen 80;
	    location /wsStart {
            proxy_pass http://backendServer$request_uri;
            proxy_http_version 1.1;
            proxy_set_header Upgrade "websocket";
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Optional: 增加代理缓冲区大小（如果传输的消息较大）
            proxy_buffer_size 128k;
            proxy_buffers 4 256k;
            proxy_busy_buffers_size 256k;

            # 避免超时导致连接断开
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
            proxy_connect_timeout 3600s;
        }

        location / {
            proxy_pass http://backendServer;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
