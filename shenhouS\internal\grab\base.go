package grab

import (
	"encoding/json"
	"fmt"
	"github.com/limitzhang87/shenhouS/internal/dao"
	"github.com/limitzhang87/shenhouS/internal/utils"
	"math/rand"
	"strings"
	"time"
)

const (
	ApkSignHash = "zkrFhoDpIPlkKlif+uTNtL1EYctTSHr7vJ/GE641gE4=\n"
	TKSep       = "&*&"
)

var DeviceToken = []string{"Nn75D6W8CPtBHEARQAfTGeKoUn+uktPa", "qD7fdEs3qhJASEFQVBfCXOK4VoIm8Eqi", "fMIqZmDcdURBTRQUAVeSXaO5Q4fOG5Be", "tx+iZNIt7ydAHVFBBVbSHLb8E9hk61YO",
	"AxFj8nlBupdEHFAQFVbHHOK9A5msNGFv", "9moQwIOpXRpBDBQVFQKHDebsSBX3D57H", "Qfv0eS31EjhATVUARRLSXPPtCFMJh1Xk", "I9+oK+ScdEZEXFEBVELXWbf9JJUYOmQr",
	"SRkvyixSKgRACQUQUQPGSPP8JNcmurr5", "uaDIphusSX1BHEREFAOCSaK4YZa2SPEg", "aLiAvmzPWqZEHVVAFAODGKapYckiz5MT", "Zcqs6QgslZlFGRQRBFKSWbO9cdyzKk8s",
	"V1m621AzGepEDBQAUEPSWaatRonGw5c/", "Zcqs6QgslZlFGRQRBFKSWbO9cdyzKk8s", "5eABD1TV8xVACEVAEFPXWKP8d+wEePhi"}

func GetDeviceToken() string {
	// 设置随机种子
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomIndex := r.Intn(len(DeviceToken))
	return DeviceToken[randomIndex]
}

func GetSidMap(req *dao.GradReq) map[string]string {
	tks := utils.SplitToken(req.FToken)
	tks = append(tks, req.GToken)
	m := make(map[string]string, len(tks))
	for _, tk := range tks {
		temp := strings.Split(tk, TKSep)
		m[temp[0]] = utils.RandStrDown(32)
	}
	return m
}

type GetAppParam struct {
	Action    string
	Url       string
	Sid       string
	Params    map[string]interface{}
	SecretKey *dao.HaluoSecretKey
}

func GetAppSendHeaderAndBody(p GetAppParam) (map[string]string, string) {
	host, _ := utils.GetHost(p.Url)
	header := map[string]string{
		"Host":             host,
		"user-agent":       "Mozilla/5.0 (Linux; Android 13; Pixel 4a Build/TQ3A.230805.001.S1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/122.0.6261.64 Mobile Safari/537.36",
		"inner_action":     p.Action,
		"inner_start_time": fmt.Sprint(time.Now().UnixMilli()),
		"systemcode":       "62",
		"chaos":            "true",
		"signature":        utils.RandStrDown(40),
		"nonce":            utils.GetGuid(),
		"timestamp":        fmt.Sprint(time.Now().UnixMilli()),
		"content-type":     "application/json; charset=UTF-8",
	}
	bodyByte, _ := json.Marshal(p.Params)
	var bodyStr string
	aesKey := utils.AESKey
	if p.SecretKey != nil {
		aesKey = p.SecretKey.Key
		additionHeaders := map[string]string{
			"sid":              p.Sid,
			"hello_token":      p.SecretKey.HelloToken,
			"fingerprint-hash": p.SecretKey.FingerprintHash,
			"key-version":      p.SecretKey.KeyVersion,
		}
		for k, v := range additionHeaders {
			header[k] = v
		}
	}
	bodyStr, _ = utils.AesEncryptNew(aesKey, bodyByte)
	return header, bodyStr
}

func DecryptBody(bodyStr string, secretKey *dao.HaluoSecretKey) string {
	aesKey := utils.AESKey
	if secretKey != nil {
		aesKey = secretKey.Key
	}
	body, _ := utils.AesDecryptNew(aesKey, bodyStr)
	return body
}

func GetSecretKey() (*dao.HaluoSecretKey, error) {
	//curTime := int(time.Now().Unix())
	secretKey, err := CurlSecretKey()
	if err != nil {
		return nil, err
	}
	return secretKey, nil
}

func CurlSecretKey() (*dao.HaluoSecretKey, error) {
	client := utils.NewHTTPClient(utils.GetProxyIpHttps())
	urlPath := "https://api.hellobike.com/secretkey"
	reqBody := GetSecretKeyBody()
	reqHeader := GetSecretKeyHeader()
	rsp, _, err := client.Post(urlPath, []byte(reqBody), reqHeader)
	if err != nil {
		return nil, err
	}
	rsp, err = utils.AesDecrypt(rsp)
	fmt.Println("获取密钥", rsp)
	var rspObj dao.HaluoRspSecretKey
	err = json.Unmarshal([]byte(rsp), &rspObj)
	if err != nil {
		return nil, err
	}

	secretKey := rspObj.Data
	secretKey.HelloToken = GetHelloToken()
	return secretKey, nil
}

func GetHelloToken() string {
	client := utils.NewHTTPClient(utils.GetProxyIpHttps())
	action := "obtain.new.hello.token"
	urlPath := "https://api.hellobike.com/api"
	params := map[string]interface{}{
		"deviceToken": GetDeviceToken(),
		"action":      action,
		"systemCode":  "62",
		"version":     "6.72.1",
		"clientId":    "",
		"sid":         "e2b5813a76814e148ba510282f83867c",
		"riskControlData": map[string]interface{}{
			"deviceLat":    0,
			"deviceLon":    0,
			"systemCode":   "62",
			"network":      "Wifi",
			"mobileNoInfo": "",
			"ssidName":     "",
			"capability":   "",
			"roam":         "",
			"batteryLevel": "7",
			"isMock":       1,
		},
	}
	reqHeader, reqBody := GetAppSendHeaderAndBody(GetAppParam{
		Action: action,
		Params: params,
		Url:    urlPath,
	})
	fmt.Println(reqBody)
	rsp, _, err := client.Post(urlPath, []byte(reqBody), reqHeader)
	if err != nil {
		return ""
	}
	rspStr, _ := utils.AesDecrypt(rsp)
	fmt.Println(rspStr)
	var rspObj dao.HaluoRspHelloToken
	err = json.Unmarshal([]byte(rspStr), &rspObj)
	if err != nil {
		fmt.Println(err)
		return ""
	}
	if rspObj.Code != 0 {
		return ""
	}
	return rspObj.Data.HelloToken
}
