package grab

import (
	"encoding/json"
	"fmt"
	"github.com/limitzhang87/shenhouS/internal/dao"
	"github.com/limitzhang87/shenhouS/internal/module/city"
	"github.com/limitzhang87/shenhouS/internal/module/tx"
	"github.com/limitzhang87/shenhouS/internal/utils"
	"strconv"
	"strings"
	"sync"
	"time"
)

const (
	ReqType1 = 1 // 行程单
	ReqType2 = 2 // 市内单
	ReqType3 = 3 // 城际单

	ApiTypeApp = 0 // APP抢单
	ApiTypeWX  = 1 // 小程序抢单

	AppVersion = "6.73.0"
	WXVersion  = "6.73.0"

	WxUserAgent  = `Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x1800312d) NetType/WIFI Language/zh_CN`
	AppUserAgent = `Mozilla/5.0 (Linux; Android 8.1.0; Redmi 5 Build/OPM1.171019.026; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/71.0.3578.99 Mobile Safari/537.36`
)

// CurlInfo 大厅单请求信息
type CurlInfo struct {
	ApiType     int    // 接口类型
	ReqType     int    // 订单类型
	GToken      string // 抢单TK
	CurToken    string // 当天TK
	Ticket      string
	Token       string
	TokenText   string
	Client      *utils.HTTPClient
	Url         string
	Header      map[string]string
	Body        map[string]interface{}
	RouteIndex  string
	FTKRouteIds map[string]map[string]string
	SecretKey   *dao.HaluoSecretKey
}

type GetInfoData struct {
	ReqType  int
	Ticket   string
	Token    string
	CityInfo *dao.GradReqCity
	DRInfo   *dao.GradReqRouteInfo // 行程单需要
}

// GetHeaderWx 获取头部信息
func GetHeaderWx(sid string) map[string]string {
	return map[string]string{
		"user-agent":   WxUserAgent,
		"Connection":   "keep-alive",
		"content-type": "application/json",
	}
}

// GetCurlInfoD 获取行程单请求信息
func GetCurlInfoD(req *dao.GradReq) []*CurlInfo {
	var res []*CurlInfo
	client := utils.NewHTTPClient(utils.GetProxyIpHttps())

	// 根据所有刷新TK创建行程
	tks := make([]string, 0)
	fTokens := utils.SplitToken(req.FToken)
	tks = append(tks, fTokens...)

	routeMap, err := GetRouteMap(client, req.GToken, req.CRute.Lat, req.CRute.Lng)
	if routeMap == nil {
		fmt.Println(err, "获取行程单错误")
	}

	fTkRouteIdMap := make(map[string]map[string]string, len(req.DRute.RouteIds))

	for i, info := range req.DRute.RouteIds {
		fTkRouteIdMap[i] = make(map[string]string)

		// 根据行程ID请求订单列表。用于获取行程的起点和终点
		tempTk := strings.Split(req.GToken, TKSep)
		cityInfo := &dao.GradReqCity{
			AdCode:   info.AdCode,
			CityCode: info.CityCode,
			Lat:      req.CRute.Lat,
			Lng:      req.CRute.Lng,
		}
		getInfoData := &GetInfoData{
			ReqType:  ReqType1,
			Ticket:   tempTk[0],
			Token:    tempTk[1],
			CityInfo: cityInfo,
			DRInfo:   info,
		}
		dList, _ := GetOneD(client, info.Id, getInfoData, req)

		// 根据当前行程，给全部刷新TK创建行程
		for _, tk := range tks {
			if _, ok := fTkRouteIdMap[i][tk]; ok {
				continue // 出现重复的tk
			}
			if tk == req.GToken {
				continue // 出现重复的tk
			}
			if dList.Data == nil || dList.Data.StartAddr == nil || dList.Data.EndAddr == nil {
				continue // 获取行程起点终点位置信息错误
			}
			// 创建行程， 如果是签单TK，则直接使用
			routeInfo := routeMap[info.Id]
			startTime := time.Unix(routeInfo.PlanStartTime/1000, 0)
			journeyId, _ := PublishJourneyWxCurl(client, tk, startTime, dList.Data.StartAddr, dList.Data.EndAddr)
			if len(journeyId) > 0 {
				fTkRouteIdMap[i][tk] = journeyId
			}
		}
	}

	tks = append(tks, req.GToken)
	for i, info := range req.DRute.RouteIds {
		cityInfo := &dao.GradReqCity{
			AdCode:   info.AdCode,
			CityCode: info.CityCode,
			Lat:      req.CRute.Lat,
			Lng:      req.CRute.Lng,
		}
		fTokenIndex := 1
		for tkI, token := range tks {
			tkText := fmt.Sprintf("刷新TK-%d", fTokenIndex)
			if tkI == len(tks)-1 {
				tkText = "接单TK"
			}
			fTokenIndex++

			temp := strings.Split(token, TKSep)
			if len(temp) != 2 {
				return nil
			}
			dInfo := &dao.GradReqRouteInfo{
				Id:       info.Id,
				AdCode:   info.AdCode,
				CityCode: info.CityCode,
				Time:     info.Time,
			}
			if token != req.GToken {
				if tempInfo, ok := fTkRouteIdMap[i]; ok {
					if drId, ok := tempInfo[token]; ok {
						dInfo.Id = drId
					} else {
						fmt.Println("小白号创建行程失败，跳过", tkText, i)
						continue
					}
				} else {
					fmt.Println("小白号创建行程失败，跳过", tkText, i)
					continue
				}
			}

			data := &GetInfoData{
				ReqType:  ReqType1,
				Ticket:   temp[0],
				Token:    temp[1],
				CityInfo: cityInfo,
				DRInfo:   dInfo,
			}
			body := GetCurlBody(data, req)
			res = append(res, &CurlInfo{
				ApiType:    req.CRute.ApiType,
				ReqType:    ReqType1,
				GToken:     req.GToken,
				CurToken:   token,
				Ticket:     temp[0],
				Token:      temp[1],
				TokenText:  tkText,
				Client:     client,
				Body:       body,
				RouteIndex: fmt.Sprint(i),
				SecretKey:  req.SecretKey,
			})

		}
	}
	return res
}

func GetOneD(client *utils.HTTPClient, id string, info *GetInfoData, req *dao.GradReq) (*dao.HaluoRspList, error) {
	data := GetBodyBase(info, req)
	data["requestType"] = ReqType1
	data["driverJourneyId"] = id
	data["driverJourneyLineId"] = ""
	data["journeyType"] = 1
	data["journeyTypeList"] = GetBodyJourneyTypeList(ReqType1)
	data["requestType1Param"] = GetBody1Param(info, req)

	urlPath := "https://taxiapi.hellobike.com/api?hitch.driver.journeyList"
	sid := utils.RandStrDown(32)
	reqHeader := GetHeaderWx(sid)
	reqBody, _ := json.Marshal(data)

	var rspBody string
	var err error

	for i := 0; i < 3; i++ {
		rspBody, _, err = client.Post(urlPath, reqBody, reqHeader)
		if err == nil {
			break
		}
	}
	if err != nil {
		return nil, err
	}

	var rspList dao.HaluoRspList

	err = json.Unmarshal([]byte(rspBody), &rspList)
	if err != nil {
		return nil, err
	}

	return &rspList, nil
}

// GetCurlInfoB 获取大厅单请求信息
func GetCurlInfoB(req *dao.GradReq) []*CurlInfo {
	var res []*CurlInfo
	client := utils.NewHTTPClient(utils.GetProxyIpHttps())
	for _, t := range req.BRute.TabIds {
		cityInfo := &dao.GradReqCity{
			AdCode:   req.CRute.AdCode,
			CityCode: city.GetCityCode(req.CRute.AdCode),
			Lat:      req.CRute.Lat,
			Lng:      req.CRute.Lng,
		}
		tks := make([]string, 0)
		fTokens := utils.SplitToken(req.FToken)
		tks = append(tks, fTokens...)
		if req.GTokenFresh {
			tks = append(tks, req.GToken)
		}
		fTokenIndex := 1
		for tkI, token := range tks {
			tkText := fmt.Sprintf("刷新TK-%d", fTokenIndex)
			if req.GTokenFresh && tkI == len(tks)-1 {
				tkText = "接单TK"
			}
			fTokenIndex++

			temp := strings.Split(token, TKSep)
			if len(temp) != 2 {
				return nil
			}
			data := &GetInfoData{
				ReqType:  t,
				Ticket:   temp[0],
				Token:    temp[1],
				CityInfo: cityInfo,
				DRInfo:   nil,
			}
			body := GetCurlBody(data, req)
			res = append(res, &CurlInfo{
				ApiType:    req.CRute.ApiType,
				ReqType:    t,
				GToken:     req.GToken,
				CurToken:   token,
				Ticket:     temp[0],
				Token:      temp[1],
				TokenText:  tkText,
				Client:     client,
				Body:       body,
				RouteIndex: "",
				SecretKey:  req.SecretKey,
			})
		}
	}
	return res
}

// GetCurlBody 获取请求信息
func GetCurlBody(info *GetInfoData, req *dao.GradReq) map[string]interface{} {
	data := GetBodyBase(info, req)
	t := info.ReqType
	data["requestType"] = info.ReqType
	switch t {
	case ReqType1:
		data["driverJourneyId"] = info.DRInfo.Id
		data["driverJourneyLineId"] = ""
		data["journeyType"] = 1
		data["journeyTypeList"] = GetBodyJourneyTypeList(t)
		data["requestType1Param"] = GetBody1Param(info, req)
	case ReqType2:
		data["driverHomePageV2"] = false
		data["journeyType"] = 1
		data["journeyTypeList"] = GetBodyJourneyTypeList(t)
		data["requestType2Param"] = GetBody2Param(info, req)
	case ReqType3:
		data["driverHomePageV2"] = false
		data["journeyType"] = 1
		data["journeyTypeList"] = GetBodyJourneyTypeList(t)
		data["requestType3Param"] = GetBody3Param(info, req)
	}
	return data
}

func GetBodyJourneyTypeList(t int) []int {
	if t == 1 {
		return []int{1, 2}
	}
	return []int{1, 2, 3}
}

func GetBodyBase(info *GetInfoData, req *dao.GradReq) map[string]interface{} {
	sort := GetSortMethod(req.DRute.OrderBy)
	if info.ReqType != ReqType1 {
		sort = GetSortMethod(req.BRute.OrderBy)
	}
	return map[string]interface{}{
		"action":                         "hitch.driver.journeyList",
		"clientId":                       "",
		"systemCode":                     "62",
		"ticket":                         info.Ticket,
		"token":                          info.Token,
		"version":                        AppVersion,
		"adCode":                         info.CityInfo.AdCode,
		"cityCode":                       info.CityInfo.CityCode,
		"lat":                            info.CityInfo.Lat,
		"lon":                            info.CityInfo.Lng,
		"apkSignHash":                    ApkSignHash,
		"bearHighwayFeeType":             0,
		"carpoolDeliverJointThreeOrders": false,
		"clickLat":                       "",
		"clickLon":                       "",
		"currentOrderGuids":              []string{},
		"pageIndex":                      1,
		"pageSize":                       10,
		//"paxJourneyId":                   "",
		"receivedPoolAgain": false,
		"skipSourceType":    0,
		"sortMethod":        sort,
		"timeRange":         0,
		"sid":               "",
		"pageSessionId":     "",
		"requestSource":     0,
		"riskControlData": map[string]interface{}{
			"deviceLat":    info.CityInfo.Lat,
			"deviceLon":    info.CityInfo.Lng,
			"systemCode":   "62",
			"network":      "Wifi",
			"mobileNoInfo": "",
			"ssidName":     "",
			"capability":   "WPA_PSK",
			"roam":         "",
			"batteryLevel": "",
			"isMock":       1,
		},
	}
}

func GetBody1Param(info *GetInfoData, req *dao.GradReq) map[string]interface{} {
	res := map[string]interface{}{
		"driverJourneyId": info.DRInfo.Id,
	}
	res["filters"] = GetFilter(info, req)
	return res
}

func GetBody2Param(info *GetInfoData, req *dao.GradReq) map[string]interface{} {
	res := map[string]interface{}{
		"endAdCode":      "",
		"endAdCodeList":  []string{},
		"endPoiName":     "",
		"endPoiNameList": []string{},
		"filters":        map[string]interface{}{},
		"lat":            req.CRute.Lat,
		"lon":            req.CRute.Lng,
		"text":           "",
	}
	res["filters"] = GetFilter(info, req)
	return res
}

func GetBody3Param(info *GetInfoData, req *dao.GradReq) map[string]interface{} {
	// 获取城市AdCode列表
	cityMap := GetAdCode(req.BRute.NeedCity)
	cityCodes := make([]string, 0, len(req.BRute.NeedCity))
	for _, adCode := range cityMap {
		if len(adCode) <= 0 {
			continue
		}
		cityCodes = append(cityCodes, adCode)
	}
	cityCodes = []string{}

	res := map[string]interface{}{
		"adCode":          info.CityInfo.AdCode,
		"cityCode":        info.CityInfo.CityCode,
		"endAdCode":       "",
		"endAdCodeList":   []string{},
		"endCityCode":     "",
		"endCityCodeList": cityCodes,
		"filters":         []map[string]interface{}{},
		"lat":             req.CRute.Lat,
		"lon":             req.CRute.Lng,
	}
	res["filters"] = GetFilter(info, req)
	return res
}

func GetFilter(info *GetInfoData, req *dao.GradReq) []map[string]interface{} {
	t := info.ReqType
	filters := make([]map[string]interface{}, 0)
	// 订单类型
	if req.CRute.OrderType > 0 {
		orderCode := GetOrderTypeCode(req.CRute.OrderType)
		filters = append(filters, map[string]interface{}{
			"code":     orderCode,
			"fromTime": "",
			"toTime":   "",
			"type":     "orderType",
		})
	}

	// 是否愿拼高速费
	if req.CRute.HighWay {
		filters = append(filters, map[string]interface{}{
			"code":     1,
			"fromTime": "",
			"toTime":   "",
			"type":     "bearHighWayFeeType",
		})
	}
	loc, _ := time.LoadLocation("Asia/Shanghai")
	if t == 1 {
		date := time.Unix(info.DRInfo.Time/1000, 0).Format(time.DateOnly)
		s, e := req.CRute.StartH, req.CRute.EndH
		if s < 0 {
			s = 0
		}
		if e < 0 {
			e = 23
		}
		startTimeStr := fmt.Sprintf("%s %02d:00:00", date, s)
		endTimeStr := fmt.Sprintf("%s %02d:59:00", date, e)
		startTime, _ := time.ParseInLocation(time.DateTime, startTimeStr, loc)
		endTime, _ := time.ParseInLocation(time.DateTime, endTimeStr, loc)
		_ = startTime
		_ = endTime
		//filters = append(filters, map[string]interface{}{
		//	"code":     4,
		//	"fromTime": startTime.Unix() * 1000,
		//	"toTime":   endTime.Unix() * 1000,
		//	"type":     "driverDepartureTimeRange",
		//})
	} else {
		// 出发日期
		if req.CRute.Date > 0 {
			filters = append(filters, map[string]interface{}{
				"code":     req.CRute.Date - 1,
				"fromTime": "",
				"toTime":   "",
				"type":     "paxDepartureDay",
			})
			// 出发时间
			if req.CRute.StartH > 0 && req.CRute.EndH > 0 {
				filters = append(filters, map[string]interface{}{
					"code":     0,
					"fromTime": fmt.Sprintf("%02d:00", req.CRute.StartH),
					"toTime":   fmt.Sprintf("%02d:59", req.CRute.EndH),
					"type":     "paxDepartureTime",
				})
			}
		}

	}
	return filters
}

// GetOrderTypeCode 获取订单类型
func GetOrderTypeCode(i int) int {
	m := map[int]int{
		0: -1, // 不限
		1: 0,  // 独享
		2: 1,  // 拼车
		3: 2,  // 舒适
		4: 4,  // 送货
	}
	return m[i]
}

// GetSortMethod 获取订单排序
func GetSortMethod(i int) int {
	m := map[int]int{
		0: 1, //智能排序
		1: 2, //时间最早
		2: 3, //离我最近
		3: 4, //价格最高
		4: 6, //顺路度最高
	}
	return m[i]
}

// GetAdCode 获取区码
func GetAdCode(cityNames []string) map[string]string {
	var wg sync.WaitGroup
	m := make(map[string]string, len(cityNames))

	client := utils.NewHTTPClient(utils.GetProxyIpHttps())
	for _, cityName := range cityNames {
		if len(cityName) <= 0 {
			continue
		}
		m[cityName] = ""
		wg.Add(1)
		go func() {
			defer wg.Done()
			rsp, _ := tx.Geo(client, cityName)
			m[cityName] = rsp["adCode"]
		}()
	}
	wg.Wait()
	return m
}

// GetSubmitBodyApp 抢单，APP接口
func GetSubmitBodyApp(req *dao.SubmitReq) map[string]interface{} {
	cityInfo := req.StartAddr
	lat := cityInfo.Lat
	lon := cityInfo.Lon

	data := map[string]interface{}{
		"action":            "hitch.driver.receiveOrder",
		"clientId":          "",
		"systemCode":        "62",
		"ticket":            req.Ticket,
		"token":             req.Token,
		"version":           AppVersion,
		"adCode":            cityInfo.AdCode,
		"cityCode":          cityInfo.CityCode,
		"lat":               cityInfo.Lat,
		"lon":               cityInfo.Lon,
		"apkSignHash":       ApkSignHash,
		"bounty":            0,
		"bountyGuid":        "",
		"driverJourneyGuid": req.DriverJourneyGuid, // 行程ID
		"driverStartAddress": map[string]interface{}{
			"adCode":     cityInfo.AdCode,
			"adName":     cityInfo.AdName,
			"addrType":   0,
			"bizArea":    cityInfo.BizArea,
			"cityCode":   cityInfo.CityCode,
			"cityName":   cityInfo.CityName,
			"lat":        cityInfo.Lat,
			"lon":        cityInfo.Lon,
			"longAddr":   cityInfo.LongAddr,
			"poiId":      "", //  cityInfo.PoiId
			"poiInfo":    "",
			"recommend":  false,
			"shortAddr":  cityInfo.ShortAddr,
			"source":     0,
			"streetInfo": "",
		},
		"hitchPercent":                 "0.99",
		"invalidOrderGreyResult":       false,
		"paxJourneyGuid":               req.Id,
		"planArriveTime":               "", //"2024-08-08 07:20:00", time.Unix(req.ArriveTime, 0).Format(time.DateTime)
		"receiveOrderDriverSourceType": 0,
		"receiveSourceType":            7,
		"relativeFriend":               0,
		"sourceType":                   1,
		"totalBounty":                  0,
		"useDefaultVehicle":            false,
		"sid":                          req.Sid,
		"riskControlData": map[string]interface{}{
			"deviceLat":    lat,
			"deviceLon":    lon,
			"systemCode":   "62",
			"network":      "Wifi",
			"mobileNoInfo": "",
			"ssidName":     "",
			"capability":   "",
			"roam":         "",
			"batteryLevel": "",
		},
	}

	if req.ProductCode > 0 {
		data["receiveProductCode"] = req.ProductCode
	}
	return data
}

// GetSubmitBodyWX 抢单， 小程序接口
func GetSubmitBodyWX(req *dao.SubmitReq) map[string]interface{} {
	cityInfo := req.StartAddr
	lat, _ := strconv.ParseFloat(cityInfo.Lat, 64)
	lon, _ := strconv.ParseFloat(cityInfo.Lon, 64)
	data := map[string]interface{}{
		"token":             req.Token,
		"ticket":            req.Ticket,
		"version":           AppVersion,
		"releaseVersion":    AppVersion,
		"systemCode":        64,
		"appName":           "AppHitchDriverApplyWXMP",
		"mobileModel":       "",
		"weChatVersion":     "8.0.49",
		"mobileSystem":      "iOS 16.6",
		"SDKVersion":        "3.5.2",
		"systemPlatform":    "ios",
		"from":              "weChat",
		"lon":               lon,
		"lat":               lat,
		"cityCode":          cityInfo.CityCode,
		"adCode":            cityInfo.AdCode,
		"action":            "hitch.driver.receiveOrder",
		"sourceType":        "1",
		"paxJourneyGuid":    req.Id,
		"driverJourneyGuid": req.DriverJourneyGuid, // 行程ID
		"driverStartAddress": map[string]interface{}{
			"lon":       lon,
			"lat":       lat,
			"longAddr":  cityInfo.LongAddr,
			"shortAddr": cityInfo.ShortAddr,
			"cityCode":  cityInfo.CityCode,
			"adCode":    cityInfo.AdCode,
			"cityName":  cityInfo.CityName,
			"poiId":     cityInfo.PoiId,
			"type":      cityInfo.Type,
		},
		"planArriveTime":    time.Unix(req.ArriveTime, 0).Format("2006-01-02 15:04"),
		"useDefaultVehicle": false,
		"receiveSourceType": 7,
		"hitchPercent":      1.0,
		"riskControlData": map[string]interface{}{
			"systemCode":   "64",
			"network":      "wifi",
			"deviceLon":    cityInfo.Lon,
			"deviceLat":    cityInfo.Lat,
			"batteryLevel": "100",
		},
	}
	if req.ProductCode > 0 {
		data["receiveProductCode"] = req.ProductCode
	}
	return data
}

// GetJourneyListBody 获取请求行程列表的参数
func GetJourneyListBody(token string, lat, lng string) map[string]interface{} {
	tk := strings.Split(token, TKSep)
	return map[string]interface{}{
		"systemPlatform": "ios",
		"mobileModel":    "",
		"mobileSystem":   "iOS 16.6",
		"SDKVersion":     "3.4.4",
		"action":         "hitch.core.checkJourneyListV2",
		"lon":            nil,
		"riskControlData": map[string]interface{}{
			"batteryLevel": "",
			"openId":       "",
			"unionId":      "",
			"deviceLon":    lng,
			"deviceLat":    lat,
			"systemCode":   "64",
			"network":      "wifi",
		},
		"systemCode":      "64",
		"version":         AppVersion,
		"ticket":          tk[0],
		"journeyTypeList": []int{1},
		"adCode":          "",
		"appName":         "AppHitchDriverApplyWXMP",
		"token":           tk[1],
		"userNewId":       nil,
		"from":            "weChat",
		"cityCode":        "",
		"lat":             nil,
		"releaseVersion":  AppVersion,
		"weChatVersion":   "8.0.49",
	}
}

// GetPublishJourneyBody 获取发布行程body
func GetPublishJourneyBody(token string, startTime time.Time, startAddr, endAddr *dao.HaluoRspAddr) map[string]interface{} {
	tk := strings.Split(token, TKSep)
	startLat, _ := strconv.ParseFloat(startAddr.Lat, 64)
	startLon, _ := strconv.ParseFloat(startAddr.Lon, 64)
	endLat, _ := strconv.ParseFloat(endAddr.Lat, 64)
	endLon, _ := strconv.ParseFloat(endAddr.Lon, 64)
	return map[string]interface{}{
		"riskControlData": map[string]interface{}{
			"systemCode":   "64",
			"network":      "wifi",
			"deviceLon":    endAddr.Lon,
			"deviceLat":    endAddr.Lat,
			"batteryLevel": "100",
		},
		"version":        AppVersion,
		"releaseVersion": AppVersion,
		"systemCode":     "64",
		"appName":        "AppHitchDriverApplyWXMP",
		"mobileModel":    "",
		"weChatVersion":  "3.9.11",
		"mobileSystem":   "iOS 16.6",
		"SDKVersion":     "3.5.3",
		"systemPlatform": "ios",
		"from":           "weChat",
		"lon":            startLon,
		"lat":            startLat,
		"cityCode":       startAddr.CityCode,
		"adCode":         startAddr.AdCode,
		"action":         "hitch.driver.publishJourney",
		"addrSign":       "d61f6fc39e8273f746b9", // TODO
		"endAddress": map[string]interface{}{
			"lon":       endLon,
			"lat":       endLat,
			"cityCode":  endAddr.CityCode,
			"adCode":    endAddr.AdCode,
			"shortAddr": endAddr.ShortAddr,
			"longAddr":  endAddr.LongAddr,
			"cityName":  endAddr.CityName,
			"poiName":   endAddr.PoiName,
			"poiFrom":   "addrList",
		},
		//"planStartTime": time.Now().Add(time.Second * 86400 * 5).Format("2006-01-02 15:04"),
		"planStartTime": startTime.Format("2006-01-02 15:04"),
		"seatCount":     4,
		"startAddress": map[string]interface{}{
			"lon":          startLon,
			"lat":          startLat,
			"longAddr":     startAddr.LongAddr,
			"shortAddr":    startAddr.ShortAddr,
			"cityCode":     startAddr.CityCode,
			"adCode":       startAddr.AdCode,
			"cityName":     startAddr.CityName,
			"businessArea": "",
		},
		"token":  tk[1],
		"ticket": tk[0],
	}
}

func GetUserInfoBody(token string) map[string]interface{} {
	tk := strings.Split(token, TKSep)
	return map[string]interface{}{
		"version":         "0.1.0",
		"from":            "h5",
		"systemCode":      "64",
		"platform":        63,
		"riskControlData": map[string]interface{}{},
		"action":          "register.person.vehicles",
		"bizScene":        "201",
		"token":           tk[1],
		"ticket":          nil,
	}
}

func GetUserInfoHeader() map[string]string {
	return map[string]string{
		"host":            "hitchregistergw.hellobike.com",
		"accept":          "application/json, text/plain, */*",
		"user-agent":      WxUserAgent,
		"content-type":    "application/json;charset=UTF-8",
		"origin":          "https://m.hellobike.com",
		"sec-fetch-site":  "same-site",
		"sec-fetch-mode":  "cors",
		"sec-fetch-dest":  "empty",
		"referer":         "https://m.hellobike.com/",
		"accept-encoding": "gzip, deflate, br",
		"accept-language": "vzh-CN,zh;q=0.9",
	}
}

func GetDetailBody(ticket, token, id, jId string) map[string]interface{} {
	return map[string]interface{}{
		"riskControlData": map[string]interface{}{
			"systemCode":   "64",
			"network":      "wifi",
			"deviceLon":    "110.29357147216797",
			"deviceLat":    "20.007600784301758",
			"batteryLevel": "100",
		},
		"version":              AppVersion,
		"releaseVersion":       AppVersion,
		"systemCode":           "64",
		"appName":              "AppHitchDriverApplyWXMP",
		"mobileModel":          "",
		"weChatVersion":        "3.9.11",
		"mobileSystem":         "iOS 16.6",
		"SDKVersion":           "3.5.3",
		"systemPlatform":       "ios",
		"from":                 "weChat",
		"lon":                  110.29357147216797,
		"lat":                  20.007600784301758,
		"cityCode":             "0898",
		"adCode":               "460105",
		"action":               "hitch.driver.journeyDetail",
		"driverJourneyGuid":    jId,
		"passengerJourneyGuid": id,
		"token":                token,
		"ticket":               ticket,
	}
}

func GetDetailHeader() map[string]string {
	return map[string]string{
		"Host":            "taxiapi.hellobike.com",
		"Connection":      "keep-alive",
		"xweb_xhr":        "1",
		"User-Agent":      WxUserAgent,
		"Content-Type":    "application/json",
		"Accept":          "*/*",
		"Sec-Fetch-Site":  "cross-site",
		"Sec-Fetch-Mode":  "cors",
		"Sec-Fetch-Dest":  "empty",
		"Referer":         "https://servicewechat.com/wxabfd42781cb9dd96/59/page-frame.html",
		"Accept-Encoding": "gzip, deflate, br",
		"Accept-Language": "zh-CN,zh;q=0.9",
	}
}

func GetSecretKeyBody() string {
	return `vH9CmZoWpyk+cppJt+YD850qXhha+k/clrD/p+2NIgGqwQekAggx03/x9gVzTsQv/nXts2cSq3gI
c+lBdfveB/buwSV8So2wDUL+8NWiCLbXvlSzIrkTA0ig90tr1UDrCJdw/v7q6m/iAQ6yOWt2rj71
MwoSViJVDwLrPtiTtB5cEF70QDnAB8v8wDaOiiy5xCw3JrrhbJ+wNjF2mgJvNEFUhEFmCIKIY0/K
WW/QEMEImoGHApiWYIYxAzBuNalfESxoBYSQRe4RcrmUtAC29QYHQvtPN5PUVvs+//uKJaGhRF4S
H2yb/hP/xaerp0vZniTQYgKgKBkYyRT9JENOUXNblcgEgzeJIwmhs4pkcwhBeHjefCb8BQA/frPQ
K7lbc41J4Mpyjw2iKMjTC+0TloRTiJzEu616jrka9VHkUPYq9lyW8dAZptrb84+Z5nk1gtzSeQB3
r80HEnFELQl5UsxAk1y5lrXGtGItfAZclKmWbEJMs6zP6u4pz2UKxEZ8aY/8P5YqR06RvvMhmpRH
CU9oMw4p+y2let29j8RRoS1RUbkop07WpmsZtu9Hnk44z+oVYxB9GKv0mLppSTjOl7OZPQqBqqSq
Z81jFcAlKijNXbq3l7TJGW6c2FXHLLyhxWLyN8Bs3+14fY6OwbuysDuUgoHijRjzOn3JxNqC3WIL
4RT6O37JyMqD3JBe35UZIPbjmhBUg32FOxu+JFHkB9UJuuTZTGy2e0O+UQl86L+okVRQ6JIg1eU0
ih7S/Xip70zRB/D2J3s1vqKX0d8rBMoqrMdeQG+ZcjQvWtTtDhS5zemwBBgOTWTJQ0aoVSUODB+7
DtRU3lLtaE09YVk5EtRVqjaluhfLtUFDsyNxhN2voyiQjRjXGjja0T9b7gzBDVx7hPG/gNOpY8rE
eyZkKcGse10GjFieqaIMUOw8DPQ80FDgugMZhig1BbzgDTvd0yfLFkkbr3atrz4gIe9g46LSbYhh
C2N7umviKCa4iqKkkzCPuiyG75bvJXOvTkKHZQrWEp8JDivdUUBO4+U8yperKmXinJZGtU2BxRTm
buJFwW6283uRzwNM3eye5tLAOuofTJF5Om63p/vGkU19EVMnd88CZ1iMfr3nxKlf+r+8x7WgJI5U
tEGJteE+8DYlalP84R702FMFG+Vq3QAg7ciT5dVaxAlXsUzamGhILYGzeD69hyx3zCp0OPjMw7p6
j1Ggoqmgx3NnCWq5f6ce3Vm1y6A4UFLsYJE64fAc99ETKWq1tkzJ0G8sniwvGeY/OX8NwtyR3MJz
1co4AemqxErMDPVYCRxCr5cN5yzqdPsw0C2UnSdfCz5gl7js7eIvlYGbXzZfeL9KU0JKjfvJ0fKu
Ixyp76nzzZtuZmiSHTwyoWMpnZGr9TbbzhlShENoOZEqYSGNYK2PLeOd+ws15mSWgtx06h1qjceY
B3ICbqr+C9nyoxejx4OLk2/lzhxzGYiOmiVBIWD186AJB2eBSpUO/XXky/G8EXlriDKvQLIsefVs
e6wlH5EOPT6SktwFtkdFpsk6sD46J4wQfX6hON2PSXaQzVIeUGTsulY3Jm/ogEyLdswy8j4yFeR1
LPkR2A7sjcOgKUBFKv6BQ+qR6W4ev7HdrlR8TNby7KliZfW6dOCgjsgO1LjhLU26wLtA
`
}

func GetSignature(message string) string {
	return utils.HmacSHA1(message, "386a09ea946d46f1b4ca3b4e3df8de45")
}

func GetSecretKeyHeader() map[string]string {
	return map[string]string{
		"chaos":        "true",
		"timestamp":    fmt.Sprintf("%d", time.Now().Unix()),
		"signature":    GetSignature(GetSecretKeyBody()),
		"nonce":        utils.GetGuid(),
		"Content-Type": "application/json; charset=UTF-8",
		"Host":         "api.hellobike.com",
		"Connection":   "Keep-Alive",
		"User-Agent":   "okhttp/3.14.9",
		//"Accept-Encoding": "gzip",
	}
}

func GetDetailV2Param(ticket, token, id string, addr *dao.HaluoRspAddr) map[string]interface{} {
	return map[string]interface{}{
		"endDistance":              "",
		"clientId":                 "",
		"ticket":                   ticket,
		"cityCode":                 addr.CityCode,
		"lon":                      addr.Lon,
		"passengerJourneyGuid":     id,
		"version":                  AppVersion,
		"token":                    token,
		"sid":                      "",
		"startDistance":            1,
		"enumPreJourneyDetailType": 6,
		"systemCode":               "62",
		"adCode":                   addr.AdCode,
		"skipSourceType":           0,
		"action":                   "hitch.driver.preJourneyDetailV2",
		"lat":                      addr.Lat,
	}
}

func GetCheckListBody(ticket, token string) map[string]interface{} {
	return map[string]interface{}{
		"mobileSystem":    "iOS 16.6",
		"weChatVersion":   "8.0.49",
		"ticket":          ticket,
		"appName":         "AppHitchDriverApplyWXMP",
		"cityCode":        "0755",
		"releaseVersion":  AppVersion,
		"journeyTypeList": "",
		"lon":             "113.883831",
		"version":         AppVersion,
		"token":           token,
		"systemPlatform":  "ios",
		"systemCode":      "64",
		"mobileModel":     "",
		"riskControlData": map[string]interface{}{
			"unionId":      "",
			"deviceLon":    "113.883831",
			"systemCode":   "64",
			"openId":       "",
			"deviceLat":    "22.554986",
			"batteryLevel": "48",
			"network":      "wifi",
		},
		"adCode":     "440306",
		"action":     "hitch.core.checkJourneyListV2",
		"from":       "weChat",
		"SDKVersion": "3.4.4",
		"lat":        "22.554986",
	}
}

func GetCheckListHeader() map[string]string {
	return map[string]string{
		"user-agent":   WxUserAgent,
		"Connection":   "keep-alive",
		"content-type": "application/json",
		"Host":         "taxiapi.hellobike.com",
	}
}

func GetCancelJourneyBody(ticket, token, id string) map[string]interface{} {
	return map[string]interface{}{
		"riskControlData": map[string]interface{}{
			"systemCode":   "64",
			"network":      "wifi",
			"deviceLon":    "120.58319091796875",
			"deviceLat":    "31.29833984375",
			"batteryLevel": "100",
		},
		"version":           AppVersion,
		"releaseVersion":    AppVersion,
		"systemCode":        "64",
		"appName":           "AppHitchDriverApplyWXMP",
		"mobileModel":       "",
		"weChatVersion":     "3.9.11",
		"mobileSystem":      "iOS 16.6",
		"SDKVersion":        "3.5.4",
		"systemPlatform":    "ios",
		"from":              "weChat",
		"action":            "hitch.driver.cancelOrder",
		"driverJourneyGuid": id,
		"status":            10,
		"cancelReason":      "",
		"token":             token,
		"ticket":            ticket,
	}
}

func GetCancelJourneyHeader() map[string]string {
	return map[string]string{
		"host":            "taxiapi.hellobike.com",
		"xweb_xhr":        "1",
		"user-agent":      WxUserAgent,
		"content-type":    "application/json",
		"accept":          "*/*",
		"sec-fetch-site":  "cross-site",
		"sec-fetch-mode":  "cors",
		"sec-fetch-dest":  "empty",
		"referer":         "https://servicewechat.com/wxabfd42781cb9dd96/60/page-frame.html",
		"accept-encoding": "gzip, deflate, br",
		"accept-language": "zh-CN,zh;q=0.9",
	}
}

func GetAutoReceiveBodyDefault(id, token, sid string, addr *dao.HaluoRspAddr) map[string]interface{} {
	tk := strings.Split(token, TKSep)
	lat, _ := strconv.ParseFloat(addr.Lat, 64)
	lon, _ := strconv.ParseFloat(addr.Lon, 64)

	//acceptStartTime := (startTime - (30 * 60)) * 1000
	//acceptEndTime := (startTime + (30 * 60)) * 1000

	if len(sid) == 0 {
		sid = utils.RandStrDown(32)
	}
	return map[string]interface{}{
		"eventType":     "CTIPageActionEvent",
		"adCode":        addr.AdCode,
		"cityCode":      addr.CityCode,
		"lat":           lat,
		"lon":           lon,
		"sdkVersion":    "1.5.4",
		"action":        "hitch.AutoReceiveOrderPage.AutoReceiveOpen",
		"bizType":       "hitch",
		"chainId":       utils.GetGuid(),
		"eventId":       utils.GetGuid(),
		"eventName":     "AutoReceiveOpenPageActionEvent",
		"fromType":      "TERMINAL",
		"parentEventId": "",
		"functionName":  "AutoReceiveOpen",
		"functionParams": map[string]interface{}{
			"settings": map[string]interface{}{
				"crossCity":         false,
				"driverJourneyGuid": id,
				"maxSeat":           4,
				"settings": []map[string]interface{}{
					{
						"rowId":         1, // 顺路度
						"selectItemIds": []string{"99"},
					},
					{
						"rowId":         2, // 起点距离公里数
						"selectItemIds": []string{"3"},
					},
					{
						"rowId":         3, // 终点距离公里数
						"selectItemIds": []string{"3"},
					},
					{
						"rowId": 4, // 可接受时间
						//"acceptEndTime":   acceptEndTime,
						//"acceptStartTime": acceptStartTime,
						"selectItemIds": []string{"2"}, // 1：前后30分钟
					},
					{
						//"acceptEndTime":   acceptEndTime,
						//"acceptStartTime": acceptStartTime,
						"rowId":         5,                       // 订单类型
						"selectItemIds": []string{"3", "1", "2"}, // 1:舒适拼, 2:拼车， 3:独享  9:特惠独享
					},
					{
						//"acceptEndTime":   acceptEndTime,
						//"acceptStartTime": acceptStartTime,
						"rowId":         9,              // 是否愿摊高速费
						"selectItemIds": []string{"20"}, // 20:不限 21:可协商或乘客全部承担  22:仅乘客全部承担
					},
					{
						//"acceptEndTime":   acceptEndTime,
						//"acceptStartTime": acceptStartTime,
						"rowId":         7,             // 座位
						"selectItemIds": []string{"4"}, // 座位数量
					},
					{
						//"acceptEndTime":   acceptEndTime,
						//"acceptStartTime": acceptStartTime,
						"rowId":         8,             // 抢单成功电话提醒
						"selectItemIds": []string{"0"}, // 0: 不提醒， 1:提醒
					},
				},
				"type": 1,
			},
		},
		"targetName": "AutoReceiveOrderPage",
		"pageId":     "AutoReceiveOrderPage-" + utils.GetGuid(),
		"actionName": "AutoReceiveOpen",
		"pageName":   "AutoReceiveOrderPage",
		"systemCode": "62",
		"version":    AppVersion,
		"token":      tk[1],
		"clientId":   "",
		"ticket":     tk[0],
		"sid":        sid,
	}
}

func GetAutoReceiveBodyAuto(id, token, sid string, rule *dao.HaluoAutoReceiveRuleData, addr *dao.HaluoRspAddr) map[string]interface{} {
	tk := strings.Split(token, TKSep)
	lat, _ := strconv.ParseFloat(addr.Lat, 64)
	lon, _ := strconv.ParseFloat(addr.Lon, 64)
	if len(sid) == 0 {
		sid = utils.RandStrDown(32)
	}

	// 获取参数
	settings2 := getRuleSetting(2, rule)
	settings3 := getRuleSetting(3, rule)
	settings4 := getRuleSetting(4, rule)

	return map[string]interface{}{
		"eventType":     "CTIPageActionEvent",
		"adCode":        addr.AdCode,
		"cityCode":      addr.CityCode,
		"lat":           lat,
		"lon":           lon,
		"sdkVersion":    "1.5.4",
		"action":        "hitch.AutoReceiveOrderPage.AutoReceiveOpen",
		"bizType":       "hitch",
		"chainId":       utils.GetGuid(),
		"eventId":       utils.GetGuid(),
		"eventName":     "AutoReceiveOpenPageActionEvent",
		"fromType":      "TERMINAL",
		"parentEventId": "",
		"functionName":  "AutoReceiveOpen",
		"functionParams": map[string]interface{}{
			"settings": map[string]interface{}{
				"crossCity":         false,
				"driverJourneyGuid": id,
				"maxSeat":           4,
				"settings": []map[string]interface{}{
					{
						"rowId":         1, // 顺路度
						"selectItemIds": []string{"99"},
					},
					settings2,
					settings3,
					settings4,
					{
						//"acceptEndTime":   acceptEndTime,
						//"acceptStartTime": acceptStartTime,
						"rowId":         5,                       // 订单类型
						"selectItemIds": []string{"3", "1", "2"}, // 1:舒适拼, 2:拼车， 3:独享  9:特惠独享
					},
					{
						//"acceptEndTime":   acceptEndTime,
						//"acceptStartTime": acceptStartTime,
						"rowId":         9,              // 是否愿摊高速费
						"selectItemIds": []string{"20"}, // 20:不限 21:可协商或乘客全部承担  22:仅乘客全部承担
					},
					{
						//"acceptEndTime":   acceptEndTime,
						//"acceptStartTime": acceptStartTime,
						"rowId":         7,             // 座位
						"selectItemIds": []string{"4"}, // 座位数量
					},
					{
						//"acceptEndTime":   acceptEndTime,
						//"acceptStartTime": acceptStartTime,
						"rowId":         8,             // 抢单成功电话提醒
						"selectItemIds": []string{"0"}, // 0: 不提醒， 1:提醒
					},
				},
				"type": 1,
			},
		},
		"targetName": "AutoReceiveOrderPage",
		"pageId":     rule.PageId,
		"actionName": "AutoReceiveOpen",
		"pageName":   "AutoReceiveOrderPage",
		"systemCode": "62",
		"version":    AppVersion,
		"token":      tk[1],
		"clientId":   "",
		"ticket":     tk[0],
		"sid":        sid,
	}
}

func getRuleSetting(id int, rule *dao.HaluoAutoReceiveRuleData) map[string]interface{} {
	switch id {
	case 2: // 起点距离
		rowState := getRuleRowStates(2, rule)
		if rowState == nil {
			return map[string]interface{}{
				"rowId":         2, // 起点距离公里数
				"selectItemIds": []string{"3"},
			}
		} else {
			return map[string]interface{}{
				"rowId":         2,
				"selectItemIds": []string{fmt.Sprint(rowState.MinStepValue)},
			}
		}
	case 3: // 终点距离
		rowState := getRuleRowStates(2, rule)
		if rowState == nil {
			return map[string]interface{}{
				"rowId":         3, // 终点距离公里数
				"selectItemIds": []string{"3"},
			}
		} else {
			return map[string]interface{}{
				"rowId":         3,
				"selectItemIds": []string{fmt.Sprint(rowState.MinStepValue)},
			}
		}
	case 4: // 可接受时间
		rowState := getRuleRowStates(4, rule)
		if rowState == nil || len(rowState.FlowItems) <= 0 {
			return map[string]interface{}{
				"rowId":         4, // 可接受时间
				"selectItemIds": []string{"1"},
			}
		} else {
			flow := rowState.FlowItems[0]
			return map[string]interface{}{
				"rowId":           4,
				"acceptEndTime":   flow.EndTime,
				"acceptStartTime": flow.StartTime,
				"selectItemIds":   []string{fmt.Sprint(flow.Id)},
			}
		}
	}
	return nil
}

func getRuleRowStates(id int, rule *dao.HaluoAutoReceiveRuleData) *dao.HaluoAutoReceiveRuleRowState {
	for _, state := range rule.FunctionParams.State.RowStates {
		if state.RowId == id {
			return &state
		}
	}
	return nil
}

func GetAutoReceiveRuleBody(id, token, sid string, startTime int64, addr *dao.HaluoRspAddr) map[string]interface{} {
	tk := strings.Split(token, TKSep)
	lat, _ := strconv.ParseFloat(addr.Lat, 64)
	lon, _ := strconv.ParseFloat(addr.Lon, 64)

	if len(sid) == 0 {
		sid = utils.RandStrDown(32)
	}
	return map[string]interface{}{
		"eventType":     "CTIPageInitEvent",
		"adCode":        addr.AdCode,
		"cityCode":      addr.CityCode,
		"lat":           lat,
		"lon":           lon,
		"sdkVersion":    "1.5.4",
		"action":        "hitch.AutoReceiveOrderPage.PageInitFunction",
		"bizType":       "hitch",
		"chainId":       utils.GetGuid(),
		"eventId":       utils.GetGuid(),
		"eventName":     "CTIPageInitEvent",
		"fromType":      "TERMINAL",
		"parentEventId": "",
		"functionName":  "PageInitFunction",
		"functionParams": map[string]interface{}{
			"initRequest": map[string]interface{}{
				"driverJourneyGuid":     id,
				"driverMaxRouteMileage": "4.43",
				"driverStartTime":       startTime * 1000,
				"endCityCode":           "",
				"freeSeat":              4,
				"maxSeat":               4,
				"startCityCode":         "",
				"type":                  1,
				"crossCity":             false,
			},
		},
		"targetName": "AutoReceiveOrderPage",
		"pageId":     "AutoReceiveOrderPage-" + utils.GetGuid(),
		"pageName":   "AutoReceiveOrderPage",
		"systemCode": "62",
		"version":    AppVersion,
		"token":      tk[1],
		"clientId":   "",
		"ticket":     tk[0],
		"sid":        sid,
	}
}

func GetRouteMap(client *utils.HTTPClient, tk string, lat, lng string) (map[string]*dao.HaluoJourneyRspItem, error) {
	data := make(map[string]*dao.HaluoJourneyRspItem)
	var rspList []*dao.HaluoJourneyRspItem
	var err error
	for i := 0; i < 3; i++ {
		rspList, err = GetJourneyList(client, tk, lat, lng)
		if err == nil {
			break
		}
	}
	if err != nil {
		return data, err
	}

	for _, item := range rspList {
		if item.OrderStatus != 10 {
			continue
		}
		data[item.JourneyGuid] = item
	}
	return data, nil
}

func GetCapIdParam(ticket, token string, reqId string, sid string) map[string]interface{} {
	data := map[string]interface{}{
		"capToken":   token,
		"language":   "zho",
		"reqId":      reqId,
		"sid":        sid,
		"action":     "risk.captcha.init",
		"systemCode": "62",
		"version":    "6.73.0",
		"token":      token,
		"clientId":   "",
		"ticket":     ticket,
	}
	return data
}

func GetCheckCaptchaParam(ticket, token string, reqId string, sid string, rspCap *dao.CapResult, rsp *dao.HaluoCapRsp) map[string]interface{} {
	data := map[string]interface{}{
		"capToken":      token,
		"captchaOutput": rspCap.Data.Seccode.CaptchaOutput,
		"challenge":     rsp.Data.Challenge,
		"genTime":       rspCap.Data.Seccode.GenTime,
		"lotNumber":     rspCap.Data.LotNumber,
		"passToken":     rspCap.Data.Seccode.PassToken,
		"reqId":         reqId,
		"riskType":      "",
		"sid":           sid,
		"systemCode":    "62",
		"action":        "risk.captcha.check",
		"version":       "6.73.0",
		"token":         token,
		"clientId":      "",
		"ticket":        ticket,
	}
	return data
}
